/**
 * Standardized error handling utilities
 * Provides consistent error processing and user-friendly messages
 */

export interface ErrorInfo {
  code: string;
  message: string;
  userMessage: string;
  severity: 'low' | 'medium' | 'high';
  retryable: boolean;
}

export enum ErrorCode {
  // Network errors
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  CONNECTION_FAILED = 'CONNECTION_FAILED',
  
  // Authentication errors
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  INVALID_API_KEY = 'INVALID_API_KEY',
  
  // API errors
  NOT_FOUND = 'NOT_FOUND',
  RATE_LIMITED = 'RATE_LIMITED',
  SERVER_ERROR = 'SERVER_ERROR',
  BAD_REQUEST = 'BAD_REQUEST',
  
  // CEP errors
  CEP_NOT_AVAILABLE = 'CEP_NOT_AVAILABLE',
  EXTENDSCRIPT_ERROR = 'EXTENDSCRIPT_ERROR',
  
  // Configuration errors
  MISSING_CONFIG = 'MISSING_CONFIG',
  INVALID_CONFIG = 'INVALID_CONFIG',
  
  // Generic errors
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  PARSING_ERROR = 'PARSING_ERROR'
}

const ERROR_PATTERNS: Array<{
  pattern: RegExp | string;
  code: ErrorCode;
  userMessage: string;
  severity: 'low' | 'medium' | 'high';
  retryable: boolean;
}> = [
  // Network errors
  {
    pattern: /timeout|timed out/i,
    code: ErrorCode.TIMEOUT_ERROR,
    userMessage: 'Request timed out. Please check your internet connection and try again.',
    severity: 'medium',
    retryable: true
  },
  {
    pattern: /network|connection|ECONNREFUSED|ENOTFOUND/i,
    code: ErrorCode.NETWORK_ERROR,
    userMessage: 'Network error. Please check your internet connection.',
    severity: 'medium',
    retryable: true
  },
  {
    pattern: /failed to connect|connection failed/i,
    code: ErrorCode.CONNECTION_FAILED,
    userMessage: 'Failed to connect to the service. Please try again later.',
    severity: 'medium',
    retryable: true
  },
  
  // Authentication errors
  {
    pattern: /unauthorized|401/i,
    code: ErrorCode.UNAUTHORIZED,
    userMessage: 'Invalid API key. Please check your credentials.',
    severity: 'high',
    retryable: false
  },
  {
    pattern: /forbidden|403/i,
    code: ErrorCode.FORBIDDEN,
    userMessage: 'Access denied. Please check your API key permissions.',
    severity: 'high',
    retryable: false
  },
  {
    pattern: /invalid.*api.*key|api.*key.*invalid/i,
    code: ErrorCode.INVALID_API_KEY,
    userMessage: 'Invalid API key format. Please check your API key.',
    severity: 'high',
    retryable: false
  },
  
  // API errors
  {
    pattern: /not found|404/i,
    code: ErrorCode.NOT_FOUND,
    userMessage: 'API endpoint not found. Please check the provider configuration.',
    severity: 'medium',
    retryable: false
  },
  {
    pattern: /rate limit|429|too many requests/i,
    code: ErrorCode.RATE_LIMITED,
    userMessage: 'Rate limit exceeded. Please wait a moment and try again.',
    severity: 'medium',
    retryable: true
  },
  {
    pattern: /server error|500|502|503|504/i,
    code: ErrorCode.SERVER_ERROR,
    userMessage: 'Server error. Please try again later.',
    severity: 'medium',
    retryable: true
  },
  {
    pattern: /bad request|400/i,
    code: ErrorCode.BAD_REQUEST,
    userMessage: 'Invalid request. Please check your configuration.',
    severity: 'medium',
    retryable: false
  },
  
  // CEP errors
  {
    pattern: /CSInterface not available|not running in CEP/i,
    code: ErrorCode.CEP_NOT_AVAILABLE,
    userMessage: 'CEP environment not available. Please run this in Adobe application.',
    severity: 'high',
    retryable: false
  },
  {
    pattern: /ExtendScript|EvalScript error/i,
    code: ErrorCode.EXTENDSCRIPT_ERROR,
    userMessage: 'Script execution error. Please try again.',
    severity: 'medium',
    retryable: true
  },
  
  // Configuration errors
  {
    pattern: /API Key required|Base URL required/i,
    code: ErrorCode.MISSING_CONFIG,
    userMessage: 'Configuration missing. Please provide required credentials.',
    severity: 'medium',
    retryable: false
  },
  {
    pattern: /invalid.*config|config.*invalid/i,
    code: ErrorCode.INVALID_CONFIG,
    userMessage: 'Invalid configuration. Please check your settings.',
    severity: 'medium',
    retryable: false
  },

  // Ollama-specific errors
  {
    pattern: /ollama.*not.*accessible|cannot connect to ollama/i,
    code: ErrorCode.CONNECTION_FAILED,
    userMessage: 'Cannot connect to Ollama service. Ensure Ollama is running.',
    severity: 'medium',
    retryable: true
  },
  {
    pattern: /ollama.*service.*error|ollama.*error/i,
    code: ErrorCode.SERVER_ERROR,
    userMessage: 'Ollama service error. Ensure Ollama is running and accessible.',
    severity: 'medium',
    retryable: true
  }
];

/**
 * Standardized error processing function
 */
export function processError(error: any): ErrorInfo {
  let errorMessage = error?.message || String(error);

  // Handle specific error types
  if (error && typeof error === 'object') {
    if (error.message) {
      errorMessage = error.message;
    }

    // Handle specific error types
    if (errorMessage.includes('timeout') || errorMessage.includes('timed out')) {
      return {
        code: ErrorCode.TIMEOUT_ERROR,
        message: errorMessage,
        userMessage: 'Request timed out. Please check your connection.',
        severity: 'medium',
        retryable: true
      };
    } else if (errorMessage.includes('ENOTFOUND') || errorMessage.includes('ECONNREFUSED')) {
      return {
        code: ErrorCode.NETWORK_ERROR,
        message: errorMessage,
        userMessage: 'Cannot connect to the service. Please check if it\'s running.',
        severity: 'medium',
        retryable: true
      };
    } else if (errorMessage.includes('401') || errorMessage.includes('Unauthorized')) {
      return {
        code: ErrorCode.UNAUTHORIZED,
        message: errorMessage,
        userMessage: 'Invalid API key. Please check your credentials.',
        severity: 'high',
        retryable: false
      };
    } else if (errorMessage.includes('403')) {
      return {
        code: ErrorCode.FORBIDDEN,
        message: errorMessage,
        userMessage: 'Access denied. Please check your API permissions.',
        severity: 'high',
        retryable: false
      };
    } else if (errorMessage.includes('429')) {
      return {
        code: ErrorCode.RATE_LIMITED,
        message: errorMessage,
        userMessage: 'Rate limit exceeded. Please try again later.',
        severity: 'medium',
        retryable: true
      };
    } else if (errorMessage.includes('JSON')) {
      return {
        code: ErrorCode.PARSING_ERROR,
        message: errorMessage,
        userMessage: 'Invalid response from service. Please check service status.',
        severity: 'medium',
        retryable: true
      };
    } else if (errorMessage.includes('Ollama')) {
      return {
        code: ErrorCode.CONNECTION_FAILED,
        message: errorMessage,
        userMessage: 'Ollama service error. Ensure Ollama is running.',
        severity: 'medium',
        retryable: true
      };
    }
  } else if (typeof error === 'string') {
    errorMessage = error;
    if (error.includes('timeout')) {
      return {
        code: ErrorCode.TIMEOUT_ERROR,
        message: errorMessage,
        userMessage: 'Request timed out. Please check your connection.',
        severity: 'medium',
        retryable: true
      };
    } else if (error.includes('Unauthorized')) {
      return {
        code: ErrorCode.UNAUTHORIZED,
        message: errorMessage,
        userMessage: 'Invalid API key. Please check your credentials.',
        severity: 'high',
        retryable: false
      };
    } else if (error.includes('Ollama')) {
      return {
        code: ErrorCode.CONNECTION_FAILED,
        message: errorMessage,
        userMessage: 'Ollama service error. Ensure Ollama is running.',
        severity: 'medium',
        retryable: true
      };
    }
  }

  // Try to match against known error patterns
  for (const pattern of ERROR_PATTERNS) {
    const isMatch = pattern.pattern instanceof RegExp
      ? pattern.pattern.test(errorMessage)
      : errorMessage.includes(pattern.pattern);

    if (isMatch) {
      return {
        code: pattern.code,
        message: errorMessage,
        userMessage: pattern.userMessage,
        severity: pattern.severity,
        retryable: pattern.retryable
      };
    }
  }

  // Default to unknown error
  return {
    code: ErrorCode.UNKNOWN_ERROR,
    message: errorMessage,
    userMessage: 'An unexpected error occurred. Please try again.',
    severity: 'medium',
    retryable: true
  };
}

/**
 * Get user-friendly error message
 */
export function getUserFriendlyMessage(error: any): string {
  return processError(error).userMessage;
}

/**
 * Check if an error is retryable
 */
export function isRetryableError(error: any): boolean {
  return processError(error).retryable;
}

/**
 * Get error severity
 */
export function getErrorSeverity(error: any): 'low' | 'medium' | 'high' {
  return processError(error).severity;
}

/**
 * Format error for logging
 */
export function formatErrorForLogging(error: any, context?: string): string {
  const errorInfo = processError(error);
  const contextStr = context ? `[${context}] ` : '';
  return `${contextStr}${errorInfo.code}: ${errorInfo.message}`;
}

/**
 * Create standardized error response
 */
export function createErrorResponse(error: any, context?: string) {
  const errorInfo = processError(error);
  
  return {
    success: false,
    error: {
      code: errorInfo.code,
      message: errorInfo.message,
      userMessage: errorInfo.userMessage,
      severity: errorInfo.severity,
      retryable: errorInfo.retryable,
      context: context || 'unknown',
      timestamp: new Date().toISOString()
    }
  };
}

/**
 * Error boundary helper for React components
 */
export class StandardError extends Error {
  public readonly code: ErrorCode;
  public readonly userMessage: string;
  public readonly severity: 'low' | 'medium' | 'high';
  public readonly retryable: boolean;
  public readonly context?: string;

  constructor(error: any, context?: string) {
    const errorInfo = processError(error);
    super(errorInfo.message);
    
    this.name = 'StandardError';
    this.code = errorInfo.code;
    this.userMessage = errorInfo.userMessage;
    this.severity = errorInfo.severity;
    this.retryable = errorInfo.retryable;
    this.context = context;
  }
}

/**
 * Async error handler wrapper
 */
export async function withErrorHandling<T>(
  operation: () => Promise<T>,
  context?: string
): Promise<{ success: true; data: T } | { success: false; error: ReturnType<typeof createErrorResponse>['error'] }> {
  try {
    const data = await operation();
    return { success: true, data };
  } catch (error) {
    const errorResponse = createErrorResponse(error, context);
    console.error(formatErrorForLogging(error, context));
    return { success: false, error: errorResponse.error };
  }
}
