import React, { useEffect, useState } from 'react';
import { useSettingsStore } from '../../stores/settingsStore';
import { SearchableModelSelect } from './SearchableModelSelect';
import { ProviderBridge } from '../../utils/cepIntegration';
import { Loader2 } from 'lucide-react';
import { processError, formatErrorForLogging } from '../../utils/errorHandling';
import { ProviderErrorBoundary } from './ProviderErrorBoundary';

export interface ProviderConfig {
  providerId: string;
  name: string;
  configType: 'apiKey' | 'baseURL';
  apiEndpoint?: string;
  placeholder: string;
  requiresApiKey: boolean;
  defaultUrl?: string;
}

interface Props {
  config: ProviderConfig;
  onSave: (config: { apiKey?: string; baseURL?: string; selectedModelId: string }) => void;
}

export const BaseProviderComponent: React.FC<Props> = ({ config, onSave }) => {
  const { getActiveProvider } = useSettingsStore();
  const activeProvider = getActiveProvider();
  
  // State management
  const [apiKey, setApiKey] = useState(
    config.configType === 'apiKey' ? (activeProvider?.apiKey || '') : ''
  );
  const [baseURL, setBaseURL] = useState(
    config.configType === 'baseURL' ? (activeProvider?.baseURL || config.defaultUrl || '') : ''
  );
  const [models, setModels] = useState<{ id: string; name: string }[]>([]);
  const [selectedModel, setSelectedModel] = useState(activeProvider?.selectedModelId || '');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [isLoadingRef, setIsLoadingRef] = useState(false); // Prevent concurrent requests

  // Load models when credentials change with debouncing and concurrency protection
  // Skip automatic loading for Ollama to prevent errors when service is not running
  useEffect(() => {
    const credential = config.configType === 'apiKey' ? apiKey : baseURL;
    if (!credential || isLoadingRef || config.providerId === 'ollama') return;

    // Debounce the request to prevent rapid-fire calls
    const timeoutId = setTimeout(() => {
      if (isLoadingRef) return; // Double-check to prevent race conditions

      setIsLoadingRef(true);
      setLoading(true);
      setError('');

      const endpoint = config.configType === 'apiKey' ? config.apiEndpoint : baseURL;

      ProviderBridge.listModels(config.providerId, endpoint, credential)
        .then((models: any) => {
          setModels(models as { id: string; name: string }[]);
          setError('');
        })
        .catch((e: any) => {
          const errorInfo = processError(e);
          console.error(formatErrorForLogging(e, `BaseProviderComponent:${config.providerId}`));
          setError(errorInfo.userMessage);
        })
        .finally(() => {
          setLoading(false);
          setIsLoadingRef(false);
        });
    }, 500); // 500ms debounce

    return () => clearTimeout(timeoutId);
  }, [apiKey, baseURL, config.providerId, config.apiEndpoint, config.configType, isLoadingRef]);

  const handleModelChange = (modelId: string) => {
    setSelectedModel(modelId);
  };

  const handleSave = () => {
    const saveConfig: { apiKey?: string; baseURL?: string; selectedModelId: string } = {
      selectedModelId: selectedModel
    };
    
    if (config.configType === 'apiKey') {
      saveConfig.apiKey = apiKey;
    } else {
      saveConfig.baseURL = baseURL;
    }
    
    onSave(saveConfig);
  };

  const renderCredentialInput = () => {
    if (config.configType === 'apiKey') {
      return (
        <div>
          <label className="block text-sm font-medium text-adobe-text-primary mb-2">
            API Key
          </label>
          <input
            type="password"
            placeholder={config.placeholder}
            value={apiKey}
            onChange={(e) => setApiKey(e.target.value)}
            className="w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary"
          />
        </div>
      );
    } else {
      return (
        <div>
          <label className="block text-sm font-medium text-adobe-text-primary mb-2">
            Base URL
          </label>
          <input
            type="text"
            placeholder={config.placeholder}
            value={baseURL}
            onChange={(e) => setBaseURL(e.target.value)}
            className="w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary"
          />
        </div>
      );
    }
  };

  const renderRetryButton = () => (
    <button
      onClick={() => {
        const credential = config.configType === 'apiKey' ? apiKey : baseURL;
        if (!credential || isLoadingRef) return; // Prevent concurrent requests

        setIsLoadingRef(true);
        setLoading(true);
        setError('');

        const endpoint = config.configType === 'apiKey' ? config.apiEndpoint : baseURL;

        ProviderBridge.listModels(config.providerId, endpoint, credential)
          .then((models: any) => {
            setModels(models as { id: string; name: string }[]);
            setError('');
          })
          .catch((e: any) => {
            const errorInfo = processError(e);
            console.error(formatErrorForLogging(e, `BaseProviderComponent:retry:${config.providerId}`));
            setError(errorInfo.userMessage);
          })
          .finally(() => {
            setLoading(false);
            setIsLoadingRef(false);
          });
      }}
      disabled={loading || isLoadingRef}
      className="text-adobe-accent text-sm hover:underline disabled:opacity-50 disabled:cursor-not-allowed"
    >
      {loading ? 'Retrying...' : 'Retry'}
    </button>
  );

  const renderModelSection = () => {
    if (loading) {
      return (
        <div className="flex items-center space-x-2 text-adobe-text-secondary">
          <Loader2 size={16} className="animate-spin" />
          <span>Loading models...</span>
        </div>
      );
    }

    if (error) {
      return (
        <div className="space-y-2">
          <p className="text-adobe-error text-sm">{error}</p>
          {renderRetryButton()}
        </div>
      );
    }

    if (models.length === 0) {
      return (
        <div className="space-y-2">
          <p className="text-adobe-warning text-sm">No models found. Check config or retry.</p>
          <p className="text-adobe-text-secondary text-xs">
            Verify your {config.configType === 'apiKey' ? 'API key has access to' : 'URL is correct for'} {config.name} models.
          </p>
          {renderRetryButton()}
        </div>
      );
    }

    return (
      <SearchableModelSelect
        models={models}
        value={selectedModel}
        onChange={handleModelChange}
        placeholder={`Search ${config.name} models...`}
      />
    );
  };

  return (
    <ProviderErrorBoundary
      providerId={config.providerId}
      onRetry={() => {
        // Reset state and retry loading models
        setError('');
        setIsLoadingRef(false);
        const credential = config.configType === 'apiKey' ? apiKey : baseURL;
        if (credential) {
          setIsLoadingRef(true);
          setLoading(true);
          const endpoint = config.configType === 'apiKey' ? config.apiEndpoint : baseURL;
          ProviderBridge.listModels(config.providerId, endpoint, credential)
            .then((models: any) => {
              setModels(models as { id: string; name: string }[]);
              setError('');
            })
            .catch((e: any) => {
              const errorInfo = processError(e);
              setError(errorInfo.userMessage);
            })
            .finally(() => {
              setLoading(false);
              setIsLoadingRef(false);
            });
        }
      }}
    >
      <div className="space-y-4">
        {/* Credential Input Section */}
        {renderCredentialInput()}

        {/* Model Selection */}
        <div>
          <label className="block text-sm font-medium text-adobe-text-primary mb-2">
            Model
          </label>
          {renderModelSection()}
        </div>

        {/* Save Button */}
        <button
          onClick={handleSave}
          disabled={!selectedModel || (config.configType === 'apiKey' ? !apiKey : !baseURL)}
          className="w-full bg-adobe-accent text-white rounded-md py-3 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-adobe-accent/90 transition-colors"
        >
          Save Configuration
        </button>
      </div>
    </ProviderErrorBoundary>
  );
};

// Provider configurations
export const PROVIDER_CONFIGS: Record<string, ProviderConfig> = {
  openai: {
    providerId: 'openai',
    name: 'OpenAI',
    configType: 'apiKey',
    apiEndpoint: 'https://api.openai.com/v1',
    placeholder: 'sk-...',
    requiresApiKey: true
  },
  anthropic: {
    providerId: 'anthropic',
    name: 'Anthropic',
    configType: 'apiKey',
    placeholder: 'sk-ant-...',
    requiresApiKey: true
  },
  gemini: {
    providerId: 'gemini',
    name: 'Google Gemini',
    configType: 'apiKey',
    apiEndpoint: 'https://generativelanguage.googleapis.com/v1beta',
    placeholder: 'AIza...',
    requiresApiKey: true
  },
  groq: {
    providerId: 'groq',
    name: 'Groq',
    configType: 'apiKey',
    apiEndpoint: 'https://api.groq.com/openai/v1',
    placeholder: 'gsk_...',
    requiresApiKey: true
  },
  deepseek: {
    providerId: 'deepseek',
    name: 'DeepSeek',
    configType: 'apiKey',
    apiEndpoint: 'https://api.deepseek.com/v1',
    placeholder: 'sk-...',
    requiresApiKey: true
  },
  openrouter: {
    providerId: 'openrouter',
    name: 'OpenRouter',
    configType: 'apiKey',
    apiEndpoint: 'https://openrouter.ai/api/v1',
    placeholder: 'sk-or-...',
    requiresApiKey: true
  },
  ollama: {
    providerId: 'ollama',
    name: 'Ollama',
    configType: 'baseURL',
    placeholder: 'http://localhost:11434',
    defaultUrl: 'http://localhost:11434',
    requiresApiKey: false
  }
};
