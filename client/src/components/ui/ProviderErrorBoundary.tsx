import { Component, ErrorInfo, ReactNode } from 'react';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface Props {
  children: ReactNode;
  providerId: string;
  onRetry?: () => void;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorCount: number;
}

export class ProviderErrorBoundary extends Component<Props, State> {
  private retryTimeout?: number;

  public state: State = {
    hasError: false,
    errorCount: 0
  };

  public static getDerivedStateFromError(error: Error): Partial<State> {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error(`Provider ${this.props.providerId} error:`, error, errorInfo);
    
    // Increment error count to prevent infinite error loops
    this.setState(prevState => ({
      errorCount: prevState.errorCount + 1
    }));

    // If too many errors, don't auto-retry
    if (this.state.errorCount >= 3) {
      console.warn(`Provider ${this.props.providerId} has failed ${this.state.errorCount} times, stopping auto-retry`);
      return;
    }

    // Auto-retry after a delay for the first few errors
    // Skip auto-retry for Ollama to prevent repeated connection errors
    if (this.state.errorCount < 2 && this.props.providerId !== 'ollama') {
      this.retryTimeout = setTimeout(() => {
        this.handleRetry();
      }, 2000 * this.state.errorCount); // Exponential backoff
    }
  }

  public componentWillUnmount() {
    if (this.retryTimeout) {
      window.clearTimeout(this.retryTimeout);
    }
  }

  private handleRetry = () => {
    this.setState({ hasError: false, error: undefined });
    if (this.props.onRetry) {
      this.props.onRetry();
    }
  };

  public render() {
    if (this.state.hasError) {
      const isMaxErrors = this.state.errorCount >= 3;
      
      return (
        <div className="flex flex-col items-center justify-center p-6 bg-adobe-bg-secondary border border-adobe-border rounded-lg">
          <AlertTriangle size={32} className="text-adobe-warning mb-3" />
          <h3 className="text-lg font-semibold text-adobe-text-primary mb-2">
            Provider Error
          </h3>
          <p className="text-adobe-text-secondary text-sm text-center mb-4">
            {isMaxErrors 
              ? `${this.props.providerId} has encountered multiple errors. Please check your configuration.`
              : `There was an error loading ${this.props.providerId}. ${this.state.errorCount < 2 ? 'Retrying automatically...' : ''}`
            }
          </p>
          
          {this.state.error && (
            <details className="mb-4 w-full">
              <summary className="text-xs text-adobe-text-secondary cursor-pointer hover:text-adobe-text-primary">
                Error Details
              </summary>
              <pre className="text-xs text-adobe-error mt-2 p-2 bg-adobe-bg-primary rounded border overflow-auto max-h-20">
                {this.state.error.message}
              </pre>
            </details>
          )}

          <div className="flex gap-2">
            <button
              onClick={this.handleRetry}
              className="flex items-center gap-2 px-3 py-1.5 bg-adobe-accent text-white rounded text-sm hover:bg-adobe-accent/90 transition-colors"
            >
              <RefreshCw size={14} />
              Try Again
            </button>
            
            {isMaxErrors && (
              <button
                onClick={() => this.setState({ hasError: false, error: undefined, errorCount: 0 })}
                className="px-3 py-1.5 border border-adobe-border text-adobe-text-primary rounded text-sm hover:bg-adobe-bg-tertiary transition-colors"
              >
                Reset
              </button>
            )}
          </div>
          
          <p className="text-xs text-adobe-text-secondary mt-3 text-center">
            Error count: {this.state.errorCount}/3
          </p>
        </div>
      );
    }

    return this.props.children;
  }
}
