import React, { useState, useEffect, useCallback } from 'react';
import { useModalStore } from '../stores/modalStore';
import { useSettingsStore } from '../../stores/settingsStore';
import { X } from 'lucide-react';
import { SearchableModelSelect } from '../ui/SearchableModelSelect';
import { SearchableProviderSelect } from '../ui/SearchableProviderSelect';
import { ErrorBoundary } from '../ErrorBoundary';
import { ProviderErrorBoundary } from '../ui/ProviderErrorBoundary';
import { throttle } from '../../utils/throttle';




export const ProviderModal: React.FC = () => {
  const { closeModal } = useModalStore();
  const { providers, activeProviderId, saveProviderSelection, loadModelsForProvider, updateProviderConfig } = useSettingsStore();

  // State for the unified modal - centralized through store
  const [selectedProviderId, setSelectedProviderId] = useState(activeProviderId || '');

  // Throttled functions to prevent rapid-fire API calls
  const throttledLoadModels = useCallback(
    throttle((providerId: string) => {
      loadModelsForProvider(providerId);
    }, 1000), // 1 second throttle
    [loadModelsForProvider]
  );

  const throttledUpdateConfig = useCallback(
    throttle((providerId: string, config: any) => {
      updateProviderConfig(providerId, config);
    }, 500), // 500ms throttle for config updates
    [updateProviderConfig]
  );

  // Get the selected provider
  const selectedProvider = providers.find(p => p.id === selectedProviderId);

  // Initialize selected model when provider changes
  useEffect(() => {
    if (selectedProvider) {
      // Use the model from store if available
      if (selectedProvider.selectedModelId) {
        // Model already selected in store, no need for local state
      } else if (selectedProvider.models.length > 0) {
        // Auto-select first model if none selected
        const firstModel = selectedProvider.models[0];
        updateProviderConfig(selectedProvider.id, { selectedModelId: firstModel.id });
      }
    }
  }, [selectedProvider, updateProviderConfig]);

  // Load models when provider is selected and configured but has no models
  // Skip automatic loading for Ollama to prevent errors when service is not running
  useEffect(() => {
    if (selectedProvider && selectedProvider.isConfigured && !selectedProvider.models.length && !selectedProvider.isLoading) {
      // Don't auto-load models for Ollama - let user manually trigger loading
      if (selectedProvider.id !== 'ollama') {
        throttledLoadModels(selectedProvider.id);
      }
    }
  }, [selectedProvider, throttledLoadModels]);

  // Handle credential input changes with throttling
  const handleCredentialChange = useCallback((value: string) => {
    if (!selectedProvider) return;

    const config = selectedProvider.configType === 'apiKey'
      ? { apiKey: value }
      : { baseURL: value };

    // Update config in store with throttling
    throttledUpdateConfig(selectedProvider.id, config);

    // If we have a valid credential, load models
    // Skip automatic loading for Ollama to prevent errors when service is not running
    if (value.trim() && selectedProvider.id !== 'ollama') {
      throttledLoadModels(selectedProvider.id);
    }
  }, [selectedProvider, throttledUpdateConfig, throttledLoadModels]);

  const handleSave = () => {
    if (!selectedProviderId || !selectedProvider) return;

    // Use values from store instead of local state
    const config = {
      apiKey: selectedProvider.configType === 'apiKey' ? selectedProvider.apiKey : undefined,
      baseURL: selectedProvider.configType === 'baseURL' ? selectedProvider.baseURL : undefined,
      selectedModelId: selectedProvider.selectedModelId,
    };

    // Validate that required fields are present
    const hasRequiredCredential = selectedProvider.configType === 'apiKey'
      ? !!config.apiKey
      : !!config.baseURL;

    if (!hasRequiredCredential || !config.selectedModelId) {
      return; // Don't save if required fields are missing
    }

    saveProviderSelection(selectedProviderId, config);
    closeModal();
  };

  // Get available models for the selected provider
  const availableModels = selectedProvider?.models.map(model => ({
    id: model.id,
    name: model.name
  })) || [];

  // Get available providers for the dropdown
  const availableProviders = providers.map(provider => ({
    id: provider.id,
    name: provider.name,
    isConfigured: provider.isConfigured
  }));

  return (
    <ErrorBoundary>
      <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm">
        <div className="bg-adobe-bg-primary border border-adobe-border rounded-xl w-[600px] shadow-2xl">
          {/* Header */}
          <div className="bg-adobe-bg-secondary border-b border-adobe-border p-6 rounded-t-xl">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold text-adobe-text-primary">
                  Configure AI Provider
                </h2>
                <p className="text-sm text-adobe-text-secondary mt-1">
                  Select and configure your AI provider
                </p>
              </div>
              <button
                onClick={closeModal}
                className="text-adobe-text-secondary hover:text-adobe-text-primary transition-colors p-1 hover:bg-adobe-bg-tertiary rounded-lg"
              >
                <X size={20} />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 space-y-6">
            {/* Provider Selection */}
            <div>
              <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
                Choose Provider
              </label>
              <SearchableProviderSelect
                providers={availableProviders}
                value={selectedProviderId}
                onChange={setSelectedProviderId}
                placeholder="Search and select a provider..."
              />
            </div>

            {/* Configuration Section - Only show if provider is selected */}
            {selectedProvider && (
              <ProviderErrorBoundary
                providerId={selectedProvider.id}
                onRetry={() => throttledLoadModels(selectedProvider.id)}
              >
                {/* Model Selection */}
                <div>
                  <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
                    Select Model
                  </label>
                  {selectedProvider.isLoading ? (
                    <div className="flex items-center space-x-2 text-adobe-text-secondary p-4 bg-adobe-bg-secondary rounded-lg border border-adobe-border">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-adobe-accent"></div>
                      <span>Loading models...</span>
                    </div>
                  ) : selectedProvider.error ? (
                    <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400 text-sm">
                      {selectedProvider.error}
                    </div>
                  ) : availableModels.length > 0 ? (
                    <SearchableModelSelect
                      models={availableModels}
                      value={selectedProvider.selectedModelId || ''}
                      onChange={(modelId) => updateProviderConfig(selectedProvider.id, { selectedModelId: modelId })}
                      placeholder={`Search ${selectedProvider.name} models...`}
                    />
                  ) : (
                    <div className="p-4 bg-adobe-bg-secondary rounded-lg border border-adobe-border text-adobe-text-secondary text-sm">
                      No models available. Configure the provider first.
                    </div>
                  )}
                </div>

                {/* API Key / Base URL Input */}
                <div>
                  <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
                    {selectedProvider.configType === 'apiKey' ? 'API Key' : 'Base URL'}
                  </label>
                  <input
                    type={selectedProvider.configType === 'apiKey' ? 'password' : 'text'}
                    placeholder={
                      selectedProvider.configType === 'apiKey'
                        ? `Enter your ${selectedProvider.name} API key...`
                        : `Enter ${selectedProvider.name} base URL...`
                    }
                    value={selectedProvider.configType === 'apiKey' ? (selectedProvider.apiKey || '') : (selectedProvider.baseURL || '')}
                    onChange={(e) => handleCredentialChange(e.target.value)}
                    className="w-full bg-adobe-bg-secondary border border-adobe-border rounded-lg px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-2 focus:ring-adobe-accent/20 outline-none transition-all"
                  />
                </div>
              </ProviderErrorBoundary>
            )}
          </div>

          {/* Footer - Only show if provider is selected */}
          {selectedProvider && (
            <div className="border-t border-adobe-border p-6 bg-adobe-bg-secondary rounded-b-xl">
              <div className="flex justify-end space-x-3">
                <button
                  onClick={closeModal}
                  className="px-4 py-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-lg transition-all"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSave}
                  disabled={
                    !selectedProviderId ||
                    !selectedProvider ||
                    (selectedProvider.configType === 'apiKey' && !selectedProvider.apiKey) ||
                    (selectedProvider.configType === 'baseURL' && !selectedProvider.baseURL) ||
                    !selectedProvider.selectedModelId
                  }
                  className="px-6 py-2 bg-adobe-accent text-white rounded-lg hover:bg-adobe-accent/90 transition-all disabled:opacity-50 disabled:cursor-not-allowed font-medium shadow-sm"
                >
                  Save & Configure
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </ErrorBoundary>
  );
};
