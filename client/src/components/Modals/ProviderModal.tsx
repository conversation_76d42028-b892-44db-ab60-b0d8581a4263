import React, { useState, useEffect, useCallback } from 'react';
import { useModalStore } from '../stores/modalStore';
import { useSettingsStore } from '../../stores/settingsStore';
import { X } from 'lucide-react';
import { SearchableModelSelect } from '../ui/SearchableModelSelect';
import { SearchableProviderSelect } from '../ui/SearchableProviderSelect';
import { ErrorBoundary } from '../ErrorBoundary';
import { ProviderErrorBoundary } from '../ui/ProviderErrorBoundary';
import { throttle } from '../../utils/throttle';
import { toast } from '../stores/toastStore';

export const ProviderModal: React.FC = () => {
  const { closeModal } = useModalStore();
  const {
    providers,
    activeProviderId,
    saveProviderSelection,
    loadModelsForProvider,
    updateProviderConfig,
    setSelectedModel
  } = useSettingsStore();

  // State for local UI changes that haven't been saved yet
  const [selectedProviderId, setSelectedProviderId] = useState(activeProviderId || '');
  const [localApiKey, setLocalApiKey] = useState('');
  const [localBaseURL, setLocalBaseURL] = useState('');
  const [localSelectedModelId, setLocalSelectedModelId] = useState('');

  // Get the selected provider from store
  const selectedProvider = providers.find(p => p.id === selectedProviderId);

  // Initialize local state when provider changes
  useEffect(() => {
    if (selectedProvider) {
      setLocalApiKey(selectedProvider.apiKey || '');
      setLocalBaseURL(selectedProvider.baseURL || '');
      setLocalSelectedModelId(selectedProvider.selectedModelId || '');
    }
  }, [selectedProvider]);

  // Handle provider selection
  const handleProviderChange = (providerId: string) => {
    setSelectedProviderId(providerId);

    // Reset local state for new provider
    const provider = providers.find(p => p.id === providerId);
    if (provider) {
      setLocalApiKey(provider.apiKey || '');
      setLocalBaseURL(provider.baseURL || '');
      setLocalSelectedModelId(provider.selectedModelId || '');
    }
  };

  // Handle credential input changes
  const handleCredentialChange = (value: string) => {
    if (!selectedProvider) return;

    if (selectedProvider.configType === 'apiKey') {
      setLocalApiKey(value);
    } else {
      setLocalBaseURL(value);
    }
  };

  // Load models when credentials are valid
  const loadModelsIfValid = useCallback(
    throttle(() => {
      if (!selectedProviderId || !selectedProvider) return;

      // Skip automatic loading for Ollama
      if (selectedProviderId === 'ollama') return;

      const hasRequiredCredential = selectedProvider.configType === 'apiKey'
        ? !!localApiKey.trim()
        : !!localBaseURL.trim();

      if (hasRequiredCredential) {
        loadModelsForProvider(selectedProviderId);
      }
    }, 1000),
    [selectedProviderId, selectedProvider, localApiKey, localBaseURL]
  );

  // Apply local changes to store
  useEffect(() => {
    if (!selectedProvider) return;

    const hasChanged =
      (selectedProvider.configType === 'apiKey' && localApiKey !== (selectedProvider.apiKey || '')) ||
      (selectedProvider.configType === 'baseURL' && localBaseURL !== (selectedProvider.baseURL || '')) ||
      localSelectedModelId !== (selectedProvider.selectedModelId || '');

    if (hasChanged) {
      const config: any = {};
      if (selectedProvider.configType === 'apiKey') {
        config.apiKey = localApiKey;
      } else {
        config.baseURL = localBaseURL;
      }

      updateProviderConfig(selectedProviderId, config);

      if (localSelectedModelId && localSelectedModelId !== selectedProvider.selectedModelId) {
        setSelectedModel(selectedProviderId, localSelectedModelId);
      }
    }

    // Trigger model loading if appropriate
    loadModelsIfValid();
  }, [localApiKey, localBaseURL, localSelectedModelId, selectedProvider, selectedProviderId]);

  // Manual load models (for Ollama)
  const handleLoadModels = () => {
    if (!selectedProviderId) return;

    // Validate configuration before loading
    if (selectedProvider?.configType === 'apiKey' && !localApiKey.trim()) {
      toast.error('Configuration Error', 'Please enter your API key first');
      return;
    }

    if (selectedProvider?.configType === 'baseURL' && !localBaseURL.trim()) {
      toast.error('Configuration Error', 'Please enter the base URL first');
      return;
    }

    loadModelsForProvider(selectedProviderId);
  };

  const handleSave = () => {
    if (!selectedProviderId || !selectedProvider) return;

    const config: any = {
      selectedModelId: localSelectedModelId,
    };

    if (selectedProvider.configType === 'apiKey') {
      config.apiKey = localApiKey;
    } else {
      config.baseURL = localBaseURL;
    }

    // Validate that required fields are present
    const hasRequiredCredential = selectedProvider.configType === 'apiKey'
      ? !!config.apiKey
      : !!config.baseURL;

    if (!hasRequiredCredential) {
      toast.error('Configuration Error', `Please enter your ${selectedProvider.configType === 'apiKey' ? 'API key' : 'base URL'}`);
      return;
    }

    if (!config.selectedModelId) {
      toast.error('Configuration Error', 'Please select a model');
      return;
    }

    saveProviderSelection(selectedProviderId, config);
    closeModal();
  };

  // Get available models for the selected provider
  const availableModels = selectedProvider?.models.map(model => ({
    id: model.id,
    name: model.name
  })) || [];

  // Get available providers for the dropdown
  const availableProviders = providers.map(provider => ({
    id: provider.id,
    name: provider.name,
    isConfigured: provider.isConfigured
  }));

  return (
    <ErrorBoundary>
      <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm">
        <div className="bg-adobe-bg-primary border border-adobe-border rounded-xl w-[600px] shadow-2xl">
          {/* Header */}
          <div className="bg-adobe-bg-secondary border-b border-adobe-border p-6 rounded-t-xl">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold text-adobe-text-primary">
                  Configure AI Provider
                </h2>
                <p className="text-sm text-adobe-text-secondary mt-1">
                  Select and configure your AI provider
                </p>
              </div>
              <button
                onClick={closeModal}
                className="text-adobe-text-secondary hover:text-adobe-text-primary transition-colors p-1 hover:bg-adobe-bg-tertiary rounded-lg"
              >
                <X size={20} />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 space-y-6">
            {/* Provider Selection */}
            <div>
              <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
                Choose Provider
              </label>
              <SearchableProviderSelect
                providers={availableProviders}
                value={selectedProviderId}
                onChange={handleProviderChange}
                placeholder="Search and select a provider..."
              />
            </div>

            {/* Configuration Section - Only show if provider is selected */}
            {selectedProvider && (
              <ProviderErrorBoundary
                providerId={selectedProvider.id}
                onRetry={handleLoadModels}
              >
                {/* API Key / Base URL Input */}
                <div>
                  <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
                    {selectedProvider.configType === 'apiKey' ? 'API Key' : 'Base URL'}
                  </label>
                  <input
                    type={selectedProvider.configType === 'apiKey' ? 'password' : 'text'}
                    placeholder={
                      selectedProvider.configType === 'apiKey'
                        ? `Enter your ${selectedProvider.name} API key...`
                        : `Enter ${selectedProvider.name} base URL...`
                    }
                    value={selectedProvider.configType === 'apiKey' ? localApiKey : localBaseURL}
                    onChange={(e) => handleCredentialChange(e.target.value)}
                    className="w-full bg-adobe-bg-secondary border border-adobe-border rounded-lg px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-2 focus:ring-adobe-accent/20 outline-none transition-all"
                  />
                </div>

                {/* Model Selection */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-semibold text-adobe-text-primary">
                      Select Model
                    </label>
                    {selectedProvider.id === 'ollama' && (
                      <button
                        onClick={handleLoadModels}
                        disabled={selectedProvider.isLoading}
                        className="text-xs text-adobe-accent hover:text-adobe-accent/80 disabled:opacity-50"
                      >
                        {selectedProvider.isLoading ? 'Loading...' : 'Load Models'}
                      </button>
                    )}
                  </div>

                  {selectedProvider.isLoading ? (
                    <div className="flex items-center space-x-2 text-adobe-text-secondary p-4 bg-adobe-bg-secondary rounded-lg border border-adobe-border">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-adobe-accent"></div>
                      <span>Loading models...</span>
                    </div>
                  ) : selectedProvider.error ? (
                    <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400 text-sm">
                      {selectedProvider.error}
                      <button
                        onClick={handleLoadModels}
                        className="ml-2 text-red-300 hover:text-red-200 underline"
                      >
                        Retry
                      </button>
                    </div>
                  ) : availableModels.length > 0 ? (
                    <SearchableModelSelect
                      models={availableModels}
                      value={localSelectedModelId}
                      onChange={setLocalSelectedModelId}
                      placeholder={`Search ${selectedProvider.name} models...`}
                    />
                  ) : (
                    <div className="p-4 bg-adobe-bg-secondary rounded-lg border border-adobe-border text-adobe-text-secondary text-sm">
                      {selectedProvider.id === 'ollama'
                        ? 'Click "Load Models" to fetch available Ollama models'
                        : 'No models available. Configure the provider first.'}
                    </div>
                  )}
                </div>
              </ProviderErrorBoundary>
            )}
          </div>

          {/* Footer - Only show if provider is selected */}
          {selectedProvider && (
            <div className="border-t border-adobe-border p-6 bg-adobe-bg-secondary rounded-b-xl">
              <div className="flex justify-end space-x-3">
                <button
                  onClick={closeModal}
                  className="px-4 py-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-lg transition-all"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSave}
                  disabled={
                    !selectedProviderId ||
                    !selectedProvider ||
                    (selectedProvider.configType === 'apiKey' && !localApiKey.trim()) ||
                    (selectedProvider.configType === 'baseURL' && !localBaseURL.trim()) ||
                    !localSelectedModelId
                  }
                  className="px-6 py-2 bg-adobe-accent text-white rounded-lg hover:bg-adobe-accent/90 transition-all disabled:opacity-50 disabled:cursor-not-allowed font-medium shadow-sm"
                >
                  Save & Configure
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </ErrorBoundary>
  );
};
