import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { CEPSettings } from '../utils/cepIntegration';
import { toast } from '../components/stores/toastStore';
import { SUCCESS_TOAST_DURATION, ERROR_TOAST_DURATION } from '../utils/constants';
import { processError, formatErrorForLogging } from '../utils/errorHandling';

export interface Model {
  id: string;
  name: string;
  description?: string;
  contextLength?: number;
  capabilities?: string[];
  isRecommended?: boolean;
}

interface Provider {
  id: string;
  name: string;
  isConfigured: boolean;
  configType: 'apiKey' | 'baseURL' | 'other';
  apiKey?: string;
  baseURL?: string;
  models: Model[];
  selectedModelId?: string;
  isLoading?: boolean;
  error?: string;
  settings?: Record<string, unknown>;
}

interface SettingsState {
  providers: Provider[];
  activeProviderId: string | undefined;
  isLoadingModels: boolean;

  // Provider actions
  setActiveProvider: (providerId: string) => void;
  updateProviderConfig: (providerId: string, config: Partial<Provider>) => void;
  setProviderModels: (providerId: string, models: Model[]) => void;
  setSelectedModel: (providerId: string, modelId: string) => void;
  updateProviderKey: (providerId: string, apiKey: string, selectedModelId?: string) => void;
  saveProviderSelection: (providerId: string, config: Partial<Provider>) => void;

  // Model actions
  loadModelsForProvider: (providerId: string) => Promise<void>;

  // CEP integration
  persistSettings: () => void;
  loadSettings: () => Promise<void>;

  // Computed getters
  getActiveProvider: () => Provider | null;
  getActiveModel: () => Model | null;
}

// Helper functions for model loading
const validateProviderConfig = (provider: Provider): string | null => {
  if (provider.configType === 'baseURL' && !provider.baseURL) {
    return 'Base URL required';
  } else if (provider.configType === 'apiKey' && !provider.apiKey) {
    return 'API Key required';
  }
  return null;
};

const transformModelsData = (models: any[], _providerId: string): Model[] => {
  return (models as any[]).map((m: any) => ({
    id: m.id || 'unknown-id',
    name: m.name || m.id || 'Unknown Model',
    description: m.description || '',
    contextLength: m.contextLength || 4096,
    isRecommended: m.isRecommended || false
  }));
};

// Removed getFriendlyErrorMessage - now using standardized error handling

export const useSettingsStore = create<SettingsState>()(
  subscribeWithSelector((set, get) => ({
  providers: [
    { id: 'openai', name: 'OpenAI', configType: 'apiKey', isConfigured: false, models: [] },
    { id: 'anthropic', name: 'Anthropic', configType: 'apiKey', isConfigured: false, models: [] },
    { id: 'gemini', name: 'Google Gemini', configType: 'apiKey', isConfigured: false, models: [] },
    { id: 'groq', name: 'Groq', configType: 'apiKey', isConfigured: false, models: [] },
    { id: 'deepseek', name: 'DeepSeek', configType: 'apiKey', isConfigured: false, models: [] },
    { id: 'openrouter', name: 'OpenRouter', configType: 'apiKey', isConfigured: false, models: [] },
    { id: 'ollama', name: 'Ollama', configType: 'baseURL', isConfigured: false, models: [] },
  ],
  activeProviderId: 'openai', // Set OpenAI as the default provider
  isLoadingModels: false,

  setActiveProvider: (providerId) => {
    // Clear models for all inactive providers to prevent background activity
    set(state => ({
      activeProviderId: providerId,
      providers: state.providers.map(p =>
        p.id !== providerId ? { ...p, models: [], isLoading: false, error: undefined } : p
      )
    }));
    get().persistSettings();

    // Load models for the newly active provider if it's configured
    // Skip automatic loading for Ollama to prevent errors when service is not running
    const provider = get().providers.find(p => p.id === providerId);
    if (provider && provider.isConfigured && (provider.apiKey || provider.baseURL) && providerId !== 'ollama') {
      get().loadModelsForProvider(providerId);
    }
  },

  updateProviderConfig: (providerId, config) => {
    set(state => ({
      providers: state.providers.map(p => 
        p.id === providerId ? { ...p, ...config, isConfigured: !!(config.apiKey || config.baseURL) } : p
      )
    }));
    get().persistSettings();
    
    // Load models after configuration
    // Skip automatic loading for Ollama to prevent errors when service is not running
    if ((config.apiKey || config.baseURL) && providerId !== 'ollama') {
      get().loadModelsForProvider(providerId);
    }
  },

  setProviderModels: (providerId, models) => {
    set(state => ({
      providers: state.providers.map(p => 
        p.id === providerId ? { ...p, models, isLoading: false, error: undefined } : p
      )
    }));
  },

  setSelectedModel: (providerId, modelId) => {
    set(state => ({
      providers: state.providers.map(p => 
        p.id === providerId ? { ...p, selectedModelId: modelId } : p
      )
    }));
    get().persistSettings();
  },

  updateProviderKey: (providerId, apiKey, selectedModelId) => {
    set(state => ({
      providers: state.providers.map(p =>
        p.id === providerId
          ? { ...p, apiKey, isConfigured: !!apiKey, selectedModelId: selectedModelId || p.selectedModelId }
          : p
      )
    }));
    get().persistSettings();
    
    // Load models after configuration
    // Skip automatic loading for Ollama to prevent errors when service is not running
    if (apiKey && providerId !== 'ollama') {
      get().loadModelsForProvider(providerId);
    }
  },

  saveProviderSelection: (providerId, config) => {
    const provider = get().providers.find(p => p.id === providerId);

    set(state => ({
      activeProviderId: providerId,
      providers: state.providers.map(p =>
        p.id === providerId
          ? { ...p, ...config, isConfigured: !!(config.apiKey || config.baseURL) }
          : p
      )
    }));
    get().persistSettings();

    // Show success toast
    toast.success(
      'Provider configured',
      `${provider?.name || providerId} has been configured successfully`,
      SUCCESS_TOAST_DURATION
    );

    // Load models after configuration
    // Skip automatic loading for Ollama to prevent errors when service is not running
    if ((config.apiKey || config.baseURL) && providerId !== 'ollama') {
      get().loadModelsForProvider(providerId);
    }
  },

  loadModelsForProvider: async (providerId) => {
    const provider = get().providers.find(p => p.id === providerId);
    if (!provider) return;

    // Only load models for the active provider (prevent background loading)
    const { activeProviderId } = get();
    if (providerId !== activeProviderId) {
      console.log(`Skipping model loading for ${providerId} - not the active provider (active: ${activeProviderId})`);
      return;
    }

    // Prevent concurrent requests for the same provider
    if (provider.isLoading) {
      console.log(`Already loading models for ${providerId}, skipping...`);
      return;
    }

    // Validate provider configuration
    const validationError = validateProviderConfig(provider);
    if (validationError) {
      set(state => ({
        providers: state.providers.map(p =>
          p.id === providerId ? { ...p, error: validationError } : p
        )
      }));
      return;
    }

    console.log(`Loading models for provider: ${providerId}`);

    // Set loading state
    set(state => ({
      providers: state.providers.map(p =>
        p.id === providerId ? { ...p, isLoading: true, error: undefined } : p
      )
    }));

    try {
      // Use CEP bridge to get models with timeout
      const { ProviderBridge } = await import('../utils/cepIntegration');

      // Add timeout to prevent hanging requests
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Request timed out after 30 seconds')), 30000);
      });

      const models = await Promise.race([
        ProviderBridge.listModels(providerId, provider.baseURL, provider.apiKey),
        timeoutPromise
      ]) as any[];

      console.log(`Received ${models.length} models for ${providerId}:`, models);

      // Transform models data
      const transformedModels = transformModelsData(models, providerId);
      console.log(`Transformed models for ${providerId}:`, transformedModels);

      // Update store with models
      get().setProviderModels(providerId, transformedModels);

      // Show success toast
      toast.success(
        'Models loaded successfully',
        `Found ${transformedModels.length} models for ${provider.name}`,
        SUCCESS_TOAST_DURATION
      );
    } catch (error: any) {
      const errorInfo = processError(error);
      console.error(formatErrorForLogging(error, `loadModelsForProvider:${providerId}`));

      // Update store with error
      set(state => ({
        providers: state.providers.map(p =>
          p.id === providerId ? { ...p, isLoading: false, error: errorInfo.userMessage } : p
        )
      }));

      // Show error toast
      toast.error(
        'Failed to load models',
        `${provider.name}: ${errorInfo.userMessage}`,
        ERROR_TOAST_DURATION
      );
    }
  },

  persistSettings: () => {
    const { activeProviderId, providers } = get();
    CEPSettings.save({
      activeProviderId,
      providers: providers.map(p => ({
        id: p.id,
        isConfigured: p.isConfigured,
        configType: p.configType,
        apiKey: p.apiKey,
        baseURL: p.baseURL,
        selectedModelId: p.selectedModelId,
        settings: p.settings
      }))
    });
  },

  loadSettings: async () => {
    try {
      const settings = await CEPSettings.load();
      if (settings.activeProviderId) {
        set({ activeProviderId: settings.activeProviderId });
      } else {
        // If no activeProviderId is saved, ensure OpenAI is set as default
        set({ activeProviderId: 'openai' });
      }
      if (settings.providers && Array.isArray(settings.providers)) {
        set(state => ({
          providers: state.providers.map(p => {
            const saved = settings.providers?.find(sp => sp.id === p.id);
            return saved ? { ...p, ...saved } : p;
          })
        }));
      }

      // Only load models for the active provider, not all configured providers
      // Skip automatic model loading for Ollama to prevent errors when service is not running
      const state = get();
      const activeProvider = state.providers.find(p => p.id === state.activeProviderId);
      if (activeProvider && activeProvider.isConfigured && (activeProvider.apiKey || activeProvider.baseURL)) {
        // Don't auto-load models for Ollama on startup to prevent connection errors
        // Users can manually load models when they open the provider modal
        if (activeProvider.id !== 'ollama') {
          get().loadModelsForProvider(activeProvider.id);
        }
      }
    } catch (error) {
      console.error('Failed to load CEP settings:', error);
    }
  },

  getActiveProvider: () => {
    const { providers, activeProviderId } = get();
    return providers.find(p => p.id === activeProviderId) || null;
  },

  getActiveModel: () => {
    const activeProvider = get().getActiveProvider();
    if (!activeProvider?.selectedModelId) return null;
    return activeProvider.models.find(m => m.id === activeProvider.selectedModelId) || null;
  }
})));
