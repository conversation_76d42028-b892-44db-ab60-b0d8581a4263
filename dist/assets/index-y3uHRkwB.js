const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./modelData-B-Cq6Rms.js","./vendor-DsceW-4w.js","./shiki-Dr4f9TeX.js","./ui-B3AhFxSy.js"])))=>i.map(i=>d[i]);
var ye=Object.defineProperty;var ve=(r,a,t)=>a in r?ye(r,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[a]=t;var P=(r,a,t)=>ve(r,typeof a!="symbol"?a+"":a,t);import{r as m,a as je,R as ie}from"./vendor-DsceW-4w.js";import{_ as U,c as Ne}from"./shiki-Dr4f9TeX.js";import{c as z,L as Q,C as M,P as Se,H as we,S as Ee,a as Ce,T as ke,D as Te,b as Re,M as J,A as Le,d as Ie,e as Pe,f as Oe,g as ce,h as de,R as X,X as _,i as se,I as V,B as Ae,j as Me,k as _e,l as De,m as W,W as $e,n as Ue,o as Fe}from"./ui-B3AhFxSy.js";(function(){const a=document.createElement("link").relList;if(a&&a.supports&&a.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))s(o);new MutationObserver(o=>{for(const n of o)if(n.type==="childList")for(const i of n.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function t(o){const n={};return o.integrity&&(n.integrity=o.integrity),o.referrerPolicy&&(n.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?n.credentials="include":o.crossOrigin==="anonymous"?n.credentials="omit":n.credentials="same-origin",n}function s(o){if(o.ep)return;o.ep=!0;const n=t(o);fetch(o.href,n)}})();var le={exports:{}},H={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ze=m,Ke=Symbol.for("react.element"),He=Symbol.for("react.fragment"),Be=Object.prototype.hasOwnProperty,qe=ze.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Ge={key:!0,ref:!0,__self:!0,__source:!0};function ue(r,a,t){var s,o={},n=null,i=null;t!==void 0&&(n=""+t),a.key!==void 0&&(n=""+a.key),a.ref!==void 0&&(i=a.ref);for(s in a)Be.call(a,s)&&!Ge.hasOwnProperty(s)&&(o[s]=a[s]);if(r&&r.defaultProps)for(s in a=r.defaultProps,a)o[s]===void 0&&(o[s]=a[s]);return{$$typeof:Ke,type:r,key:n,ref:i,props:o,_owner:qe.current}}H.Fragment=He;H.jsx=ue;H.jsxs=ue;le.exports=H;var e=le.exports,Y={},re=je;Y.createRoot=re.createRoot,Y.hydrateRoot=re.hydrateRoot;const Je=r=>(a,t,s)=>{const o=s.subscribe;return s.subscribe=(i,c,u)=>{let d=i;if(c){const l=u?.equalityFn||Object.is;let f=i(s.getState());d=g=>{const y=i(g);if(!l(f,y)){const p=f;c(f=y,p)}},u?.fireImmediately&&c(f,f)}return o(d)},r(a,t,s)},Ve=Je,We=3e4,Ye=2,B=1e4,K=1e3,Qe=4e3,ae=3e3,oe=5e3,Ot=4096,At=8192,Mt=32e3,_t=128e3,Dt=2e5,Xe="sahAI_settings",C="sahai-chat-history";let q=null;const A=()=>typeof window<"u"&&!!window.CSInterface,F=()=>{if(!q&&A())try{q=new window.CSInterface,console.log("CSInterface initialized successfully")}catch(r){console.error("Failed to initialize CSInterface:",r)}return q},me=()=>{if(!A()){console.warn("Not running in CEP environment");return}const r=F();if(!r)return;r.addEventListener("com.adobe.csxs.events.ThemeColorChanged",t=>{console.log("Theme changed:",t)});const a=r.getHostEnvironment();console.log("Host environment:",a),r.evalScript("SahAI.getAppInfo()",t=>{try{if(!t||t.trim()===""){console.warn("Empty response from ExtendScript");return}const s=JSON.parse(t);console.log("ExtendScript response:",s)}catch(s){console.error("Failed to parse ExtendScript response:",s,"Raw result:",t)}})},Ze=r=>r.startsWith("EvalScript error")||!r||r.trim()==="",et=r=>{try{return JSON.parse(r)}catch{return{success:!0,data:r}}},tt=r=>typeof r=="object"&&r!==null&&r.success===!1,T=(r,a=We,t=Ye)=>new Promise((s,o)=>{const n=F();if(!n){o(new Error("CSInterface not available - not running in CEP environment"));return}let i=0;const c=()=>{i++;const u=setTimeout(()=>{i<=t?(console.warn(`ExtendScript execution attempt ${i} timed out, retrying...`),c()):o(new Error(`ExtendScript execution timed out after ${a}ms (${t+1} attempts)`))},a);try{n.evalScript(r,d=>{clearTimeout(u);try{if(Ze(d)){if(i<=t){console.warn(`Retryable error on attempt ${i}, retrying...`),setTimeout(c,K);return}o(new Error(d.startsWith("EvalScript error")?`ExtendScript Error: ${d}`:"Empty response from ExtendScript after all retries"));return}const l=et(d);if(tt(l)){if(i<=t){console.warn(`ExtendScript returned failure on attempt ${i}, retrying...`),setTimeout(c,K);return}o(new Error(l.message||"ExtendScript execution failed"));return}s(typeof l=="object"&&l!==null?l:{success:!0,data:l})}catch(l){if(i<=t){console.warn(`Error processing response on attempt ${i}, retrying...`),setTimeout(c,K);return}o(new Error(`Failed to process ExtendScript response: ${l}`))}})}catch(d){if(clearTimeout(u),i<=t){console.warn(`Error executing ExtendScript on attempt ${i}, retrying...`),setTimeout(c,K);return}o(new Error(`Failed to execute ExtendScript: ${d}`))}};c()});class I{static async save(a){const t=JSON.stringify(a);try{if(A())try{const s=await T(`saveSettings(${JSON.stringify(a)})`,B);if(s.success)console.log("Settings saved to CEP storage successfully");else throw new Error(s.message||"CEP save failed")}catch(s){console.warn("CEP storage save failed, falling back to localStorage:",s)}localStorage.setItem(this.SETTINGS_KEY,t),console.log("Settings saved to localStorage successfully")}catch(s){console.error("All settings save methods failed:",s);try{localStorage.setItem(this.SETTINGS_KEY,t)}catch(o){throw new Error(`Failed to save settings: ${s}. LocalStorage also failed: ${o}`)}}}static async load(){try{const a=[{name:"CEP Storage",load:async()=>{if(A()){const t=await T("loadSettings()",B);if(t.success)return t.data&&Object.keys(t.data).length>0?t.data:null}return null}},{name:"LocalStorage",load:async()=>{const t=localStorage.getItem(this.SETTINGS_KEY);if(t)try{const s=JSON.parse(t);if(s&&Object.keys(s).length>0)return s}catch(s){console.warn("Failed to parse localStorage settings:",s)}return null}}];for(const t of a)try{const s=await t.load();if(s)return console.log(`Settings loaded from ${t.name} successfully`),s}catch(s){console.warn(`Failed to load settings from ${t.name}:`,s)}return console.log("No existing settings found in any storage, returning defaults"),{providers:[]}}catch(a){return console.error("All settings load methods failed:",a),{providers:[]}}}static async exportSettings(){const a=await this.load();return JSON.stringify(a,null,2)}static async importSettings(a){try{const t=JSON.parse(a);await this.save(t)}catch{throw new Error("Invalid settings format")}}static async clearSettings(){try{if(A())try{await T("saveSettings({})",B)}catch(a){console.warn("Failed to clear CEP storage:",a)}localStorage.removeItem(this.SETTINGS_KEY),console.log("Settings cleared successfully")}catch(a){throw new Error(`Failed to clear settings: ${a}`)}}}P(I,"SETTINGS_KEY",Xe);class Z{static async checkProviderStatus(a,t){const s=Date.now();try{const{ProviderBridge:o}=await U(async()=>{const{ProviderBridge:i}=await Promise.resolve().then(()=>xe);return{ProviderBridge:i}},void 0,import.meta.url);return{isOnline:(await o.listModels(a,t.baseURL,t.apiKey)).length>0,latency:Date.now()-s}}catch(o){return{isOnline:!1,error:o.message||String(o),latency:Date.now()-s}}}}Promise.prototype.timeout||(Promise.prototype.timeout=function(r){return Promise.race([this,new Promise((a,t)=>setTimeout(()=>t(new Error(`Operation timed out after ${r}ms`)),r))])});const st={async listModels(r,a,t){return new Promise((s,o)=>{const n=F();if(!n){o(new Error("CSInterface not available - not running in CEP environment"));return}const i=`listModels("${r}", "${a||""}", "${t||""}")`;n.evalScript(i,c=>{try{if(!c||c.trim()===""||c.trim()==="undefined")return o(new Error("ExtendScript returned an empty response. Check if the script is properly loaded."));if(c.startsWith("EvalScript error"))return o(new Error(`ExtendScript execution failed: ${c}`));const u=JSON.parse(c);if(u.success){const d=u.data.map(l=>({id:l.id||"unknown-id",name:l.name||l.id||"Unknown Model",description:l.description||"",contextLength:l.contextLength||l.context_length||4096,isRecommended:l.isRecommended||l.is_recommended||!1}));s(d)}else throw new Error(u.message||"An unknown ExtendScript error occurred.")}catch(u){o(new Error(`Failed to parse response from host: ${u.message}. Response: ${c}`))}})})},async pullModel(r,a,t){if(r!=="ollama")throw new Error("Pull only supported for Ollama");return new Promise((s,o)=>{const n=F();if(!n){o(new Error("CSInterface not available - not running in CEP environment"));return}const i=t.replace("http://","").split(":")[0],c=parseInt(t.split(":")[2]||"11434",10),u=`
        try {
          var result = makeRequest('${i}', '/api/pull', 'POST', null, ${c}, JSON.stringify({name: '${a}'}));
          JSON.stringify({success: true, message: 'Model pull initiated'});
        } catch (e) {
          JSON.stringify({success: false, message: e.toString()});
        }
      `;n.evalScript(u,d=>{try{const l=JSON.parse(d);l.success?s(l):o(new Error(l.message))}catch(l){o(new Error(`Failed to parse pull response: ${l}`))}})})},async getFallbackModels(r){const{getFallbackModels:a}=await U(async()=>{const{getFallbackModels:t}=await import("./modelData-B-Cq6Rms.js");return{getFallbackModels:t}},__vite__mapDeps([0,1,2,3]),import.meta.url);return a(r)}},xe=Object.freeze(Object.defineProperty({__proto__:null,CEPSettings:I,ProviderBridge:st,ProviderStatusChecker:Z,executeExtendScript:T,getCSInterface:F,initializeCEP:me,isCEPEnvironment:A},Symbol.toStringTag,{value:"Module"})),L=z((r,a)=>({toasts:[],addToast:t=>{const s=`toast-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,o={...t,id:s,isVisible:!0,duration:t.duration||Qe};r(n=>({toasts:[...n.toasts,o]})),setTimeout(()=>{a().removeToast(s)},o.duration)},removeToast:t=>{r(s=>({toasts:s.toasts.filter(o=>o.id!==t)}))},clearAllToasts:()=>{r({toasts:[]})}})),R={success:(r,a,t)=>{L.getState().addToast({type:"success",title:r,message:a,duration:t})},error:(r,a,t)=>{L.getState().addToast({type:"error",title:r,message:a,duration:t})},warning:(r,a,t)=>{L.getState().addToast({type:"warning",title:r,message:a,duration:t})},info:(r,a,t)=>{L.getState().addToast({type:"info",title:r,message:a,duration:t})}},rt=[{pattern:/timeout|timed out/i,code:"TIMEOUT_ERROR",userMessage:"Request timed out. Please check your internet connection and try again.",severity:"medium",retryable:!0},{pattern:/network|connection|ECONNREFUSED|ENOTFOUND/i,code:"NETWORK_ERROR",userMessage:"Network error. Please check your internet connection.",severity:"medium",retryable:!0},{pattern:/failed to connect|connection failed/i,code:"CONNECTION_FAILED",userMessage:"Failed to connect to the service. Please try again later.",severity:"medium",retryable:!0},{pattern:/unauthorized|401/i,code:"UNAUTHORIZED",userMessage:"Invalid API key. Please check your credentials.",severity:"high",retryable:!1},{pattern:/forbidden|403/i,code:"FORBIDDEN",userMessage:"Access denied. Please check your API key permissions.",severity:"high",retryable:!1},{pattern:/invalid.*api.*key|api.*key.*invalid/i,code:"INVALID_API_KEY",userMessage:"Invalid API key format. Please check your API key.",severity:"high",retryable:!1},{pattern:/not found|404/i,code:"NOT_FOUND",userMessage:"API endpoint not found. Please check the provider configuration.",severity:"medium",retryable:!1},{pattern:/rate limit|429|too many requests/i,code:"RATE_LIMITED",userMessage:"Rate limit exceeded. Please wait a moment and try again.",severity:"medium",retryable:!0},{pattern:/server error|500|502|503|504/i,code:"SERVER_ERROR",userMessage:"Server error. Please try again later.",severity:"medium",retryable:!0},{pattern:/bad request|400/i,code:"BAD_REQUEST",userMessage:"Invalid request. Please check your configuration.",severity:"medium",retryable:!1},{pattern:/CSInterface not available|not running in CEP/i,code:"CEP_NOT_AVAILABLE",userMessage:"CEP environment not available. Please run this in Adobe application.",severity:"high",retryable:!1},{pattern:/ExtendScript|EvalScript error/i,code:"EXTENDSCRIPT_ERROR",userMessage:"Script execution error. Please try again.",severity:"medium",retryable:!0},{pattern:/API Key required|Base URL required/i,code:"MISSING_CONFIG",userMessage:"Configuration missing. Please provide required credentials.",severity:"medium",retryable:!1},{pattern:/invalid.*config|config.*invalid/i,code:"INVALID_CONFIG",userMessage:"Invalid configuration. Please check your settings.",severity:"medium",retryable:!1},{pattern:/ollama.*not.*accessible|cannot connect to ollama/i,code:"CONNECTION_FAILED",userMessage:"Cannot connect to Ollama service. Ensure Ollama is running.",severity:"medium",retryable:!0},{pattern:/ollama.*service.*error|ollama.*error/i,code:"SERVER_ERROR",userMessage:"Ollama service error. Ensure Ollama is running and accessible.",severity:"medium",retryable:!0}];function be(r){let a=r?.message||String(r);if(r&&typeof r=="object"){if(r.message&&(a=r.message),a.includes("timeout")||a.includes("timed out"))return{code:"TIMEOUT_ERROR",message:a,userMessage:"Request timed out. Please check your connection.",severity:"medium",retryable:!0};if(a.includes("ENOTFOUND")||a.includes("ECONNREFUSED"))return{code:"NETWORK_ERROR",message:a,userMessage:"Cannot connect to the service. Please check if it's running.",severity:"medium",retryable:!0};if(a.includes("401")||a.includes("Unauthorized"))return{code:"UNAUTHORIZED",message:a,userMessage:"Invalid API key. Please check your credentials.",severity:"high",retryable:!1};if(a.includes("403"))return{code:"FORBIDDEN",message:a,userMessage:"Access denied. Please check your API permissions.",severity:"high",retryable:!1};if(a.includes("429"))return{code:"RATE_LIMITED",message:a,userMessage:"Rate limit exceeded. Please try again later.",severity:"medium",retryable:!0};if(a.includes("JSON"))return{code:"PARSING_ERROR",message:a,userMessage:"Invalid response from service. Please check service status.",severity:"medium",retryable:!0};if(a.includes("Ollama"))return{code:"CONNECTION_FAILED",message:a,userMessage:"Ollama service error. Ensure Ollama is running.",severity:"medium",retryable:!0}}else if(typeof r=="string"){if(a=r,r.includes("timeout"))return{code:"TIMEOUT_ERROR",message:a,userMessage:"Request timed out. Please check your connection.",severity:"medium",retryable:!0};if(r.includes("Unauthorized"))return{code:"UNAUTHORIZED",message:a,userMessage:"Invalid API key. Please check your credentials.",severity:"high",retryable:!1};if(r.includes("Ollama"))return{code:"CONNECTION_FAILED",message:a,userMessage:"Ollama service error. Ensure Ollama is running.",severity:"medium",retryable:!0}}for(const t of rt)if(t.pattern instanceof RegExp?t.pattern.test(a):a.includes(t.pattern))return{code:t.code,message:a,userMessage:t.userMessage,severity:t.severity,retryable:t.retryable};return{code:"UNKNOWN_ERROR",message:a,userMessage:"An unexpected error occurred. Please try again.",severity:"medium",retryable:!0}}function at(r,a){const t=be(r);return`${a?`[${a}] `:""}${t.code}: ${t.message}`}const ot=r=>r.configType==="baseURL"&&!r.baseURL?"Base URL required":r.configType==="apiKey"&&!r.apiKey?"API Key required":null,nt=(r,a)=>r.map(t=>({id:t.id||"unknown-id",name:t.name||t.id||"Unknown Model",description:t.description||"",contextLength:t.contextLength||4096,isRecommended:t.isRecommended||!1})),D=z()(Ve((r,a)=>({providers:[{id:"openai",name:"OpenAI",configType:"apiKey",isConfigured:!1,models:[]},{id:"anthropic",name:"Anthropic",configType:"apiKey",isConfigured:!1,models:[]},{id:"gemini",name:"Google Gemini",configType:"apiKey",isConfigured:!1,models:[]},{id:"groq",name:"Groq",configType:"apiKey",isConfigured:!1,models:[]},{id:"deepseek",name:"DeepSeek",configType:"apiKey",isConfigured:!1,models:[]},{id:"openrouter",name:"OpenRouter",configType:"apiKey",isConfigured:!1,models:[]},{id:"ollama",name:"Ollama",configType:"baseURL",isConfigured:!1,models:[]}],activeProviderId:"openai",isLoadingModels:!1,setActiveProvider:t=>{r(o=>({activeProviderId:t,providers:o.providers.map(n=>n.id!==t?{...n,models:[],isLoading:!1,error:void 0}:n)})),a().persistSettings();const s=a().providers.find(o=>o.id===t);s&&s.isConfigured&&(s.apiKey||s.baseURL)&&t!=="ollama"&&a().loadModelsForProvider(t)},updateProviderConfig:(t,s)=>{r(o=>({providers:o.providers.map(n=>n.id===t?{...n,...s,isConfigured:!!(s.apiKey||s.baseURL)}:n)})),a().persistSettings(),(s.apiKey||s.baseURL)&&t!=="ollama"&&a().loadModelsForProvider(t)},setProviderModels:(t,s)=>{r(o=>({providers:o.providers.map(n=>n.id===t?{...n,models:s,isLoading:!1,error:void 0}:n)}))},setSelectedModel:(t,s)=>{r(o=>({providers:o.providers.map(n=>n.id===t?{...n,selectedModelId:s}:n)})),a().persistSettings()},updateProviderKey:(t,s,o)=>{r(n=>({providers:n.providers.map(i=>i.id===t?{...i,apiKey:s,isConfigured:!!s,selectedModelId:o||i.selectedModelId}:i)})),a().persistSettings(),s&&t!=="ollama"&&a().loadModelsForProvider(t)},saveProviderSelection:(t,s)=>{const o=a().providers.find(n=>n.id===t);r(n=>({activeProviderId:t,providers:n.providers.map(i=>i.id===t?{...i,...s,isConfigured:!!(s.apiKey||s.baseURL)}:i)})),a().persistSettings(),R.success("Provider configured",`${o?.name||t} has been configured successfully`,ae),(s.apiKey||s.baseURL)&&t!=="ollama"&&a().loadModelsForProvider(t)},loadModelsForProvider:async t=>{const s=a().providers.find(n=>n.id===t);if(!s)return;if(s.isLoading){console.log(`Already loading models for ${t}, skipping...`);return}if(t==="ollama"){const n=s.baseURL||"http://localhost:11434";try{const i=`${n.replace(/\/$/,"")}/api/tags`;if(!(await fetch(i,{method:"GET",signal:AbortSignal.timeout(5e3)})).ok)throw new Error(`Ollama service not accessible at ${n}`)}catch{r(c=>({providers:c.providers.map(u=>u.id===t?{...u,isLoading:!1,error:"Cannot connect to Ollama service. Ensure it is running."}:u)})),R.error("Ollama Connection Failed","Cannot connect to Ollama service. Ensure it is running.",oe);return}}const o=ot(s);if(o){r(n=>({providers:n.providers.map(i=>i.id===t?{...i,error:o}:i)}));return}console.log(`Loading models for provider: ${t}`),r(n=>({providers:n.providers.map(i=>i.id===t?{...i,isLoading:!0,error:void 0}:i)}));try{const{ProviderBridge:n}=await U(async()=>{const{ProviderBridge:d}=await Promise.resolve().then(()=>xe);return{ProviderBridge:d}},void 0,import.meta.url),i=new Promise((d,l)=>{setTimeout(()=>l(new Error("Request timed out after 30 seconds")),3e4)}),c=await Promise.race([n.listModels(t,s.baseURL,s.apiKey),i]);console.log(`Received ${c.length} models for ${t}:`,c);const u=nt(c,t);console.log(`Transformed models for ${t}:`,u),a().setProviderModels(t,u),u.length>0&&!s.selectedModelId&&a().setSelectedModel(t,u[0].id),R.success("Models loaded successfully",`Found ${u.length} models for ${s.name}`,ae)}catch(n){const i=be(n);console.error(at(n,`loadModelsForProvider:${t}`)),r(u=>({providers:u.providers.map(d=>d.id===t?{...d,isLoading:!1,error:i.userMessage}:d)}));const c=s?.name||t;R.error(`Failed to load ${c} models`,i.userMessage,oe)}},persistSettings:()=>{const{activeProviderId:t,providers:s}=a();I.save({activeProviderId:t,providers:s.map(o=>({id:o.id,isConfigured:o.isConfigured,configType:o.configType,apiKey:o.apiKey,baseURL:o.baseURL,selectedModelId:o.selectedModelId,settings:o.settings}))})},loadSettings:async()=>{try{const t=await I.load(),s=t.activeProviderId||"openai";r({activeProviderId:s}),t.providers&&Array.isArray(t.providers)&&r(i=>({providers:i.providers.map(c=>{const u=t.providers?.find(d=>d.id===c.id);return u?{...c,...u}:c})}));const o=a(),n=o.providers.find(i=>i.id===o.activeProviderId);n&&n.isConfigured&&(n.apiKey||n.baseURL)&&n.id!=="ollama"&&a().loadModelsForProvider(n.id)}catch(t){console.error("Failed to load CEP settings:",t),r({activeProviderId:"openai"})}},getActiveProvider:()=>{const{providers:t,activeProviderId:s}=a();return t.find(o=>o.id===s)||null},getActiveModel:()=>{const t=a().getActiveProvider();return t?.selectedModelId&&t.models.find(s=>s.id===t.selectedModelId)||null}}))),$=z(r=>({modal:null,openModal:a=>r({modal:a}),closeModal:()=>r({modal:null})})),ee=z((r,a)=>({messages:[],isLoading:!1,addMessage:t=>{const s={...t,id:crypto.randomUUID(),timestamp:Date.now()};r(n=>({messages:[...n.messages,s]}));const o=a().currentSession;if(o){const n=()=>{U(async()=>{const{useHistoryStore:i}=await Promise.resolve().then(()=>ne);return{useHistoryStore:i}},void 0,import.meta.url).then(({useHistoryStore:i})=>{const c=i.getState(),u=c.sessions.find(d=>d.id===o);if(u){const d=a().messages,l={...u,messages:d,updatedAt:Date.now(),title:u.title===`Chat ${new Date(u.createdAt).toLocaleDateString()}`&&d[0]?.content.slice(0,50)+"..."||u.title};c.saveSession(l)}}).catch(i=>{console.error("Failed to import history store:",i)})};window.requestIdleCallback?window.requestIdleCallback(n):setTimeout(n,0)}},setLoading:t=>r({isLoading:t}),createNewSession:()=>{const t=crypto.randomUUID();return r({messages:[],currentSession:t}),U(async()=>{const{useHistoryStore:s}=await Promise.resolve().then(()=>ne);return{useHistoryStore:s}},void 0,import.meta.url).then(({useHistoryStore:s})=>{s.getState().createSession()}).catch(s=>{console.error("Failed to import history store:",s)}),t},loadSession:(t,s)=>{r({currentSession:t,messages:s})},clearMessages:()=>{r({messages:[],currentSession:void 0})}})),it=()=>{const{getActiveProvider:r}=D(),[a,t]=m.useState({isOnline:null,isChecking:!1}),s=r();m.useEffect(()=>{let n;const i=async()=>{if(!s?.isConfigured){t({isOnline:null,isChecking:!1});return}t(c=>({...c,isChecking:!0,error:void 0}));try{const c=await Z.checkProviderStatus(s.id,{apiKey:s.apiKey,baseURL:s.baseURL});t({isOnline:c.isOnline,latency:c.latency,isChecking:!1})}catch(c){t({isOnline:!1,isChecking:!1,error:c.message})}};return s?.isConfigured&&(i(),n=setInterval(i,3e4)),()=>{n&&clearInterval(n)}},[s]);const o=()=>{const n="w-2.5 h-2.5 rounded-full transition-all duration-300 shadow-sm hover:scale-110 cursor-pointer";return a.isChecking?`${n} bg-adobe-warning animate-pulse shadow-adobe-warning/40`:a.isOnline===!0?`${n} bg-adobe-success shadow-adobe-success/40`:`${n} bg-adobe-error shadow-adobe-error/40`};return e.jsx("div",{className:o()})},ct=()=>{const{getActiveProvider:r,getActiveModel:a,loadSettings:t}=D(),{openModal:s}=$(),{createNewSession:o}=ee(),[,n]=m.useState({}),i=r(),c=a(),u=i?.isLoading||!1,d=m.useMemo(()=>i?i.id==="ollama"&&i.isLoading?"Ollama • Loading models...":u?`${i.name} • Loading models...`:c?`${i.name} • ${c.name}`:`${i.name} • Select Model`:"Select AI Provider & Model",[i,c,u]);return m.useEffect(()=>{t()},[t]),m.useEffect(()=>D.subscribe(()=>n({})),[]),e.jsxs("header",{className:"flex items-center justify-between px-4 py-3 border-b border-adobe-border bg-adobe-bg-secondary shadow-sm",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("button",{onClick:()=>s("status"),className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all flex items-center justify-center",children:e.jsx(it,{})}),e.jsxs("button",{onClick:()=>s("provider"),className:"flex items-center space-x-2 text-sm font-medium text-adobe-text-primary hover:text-adobe-accent transition-colors group",title:"Select AI Provider & Model",children:[e.jsx("span",{className:"max-w-[300px] truncate",children:d}),u?e.jsx(Q,{size:14,className:"animate-spin text-adobe-text-secondary"}):e.jsx(M,{size:14,className:"text-adobe-text-secondary group-hover:text-adobe-accent transition-colors"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{onClick:o,className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all",title:"New Chat",children:e.jsx(Se,{size:16})}),e.jsx("div",{className:"h-4 w-px bg-adobe-border"}),e.jsx("button",{onClick:()=>s("chat-history"),className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all",title:"Chat History",children:e.jsx(we,{size:16})}),e.jsx("button",{onClick:()=>s("settings"),className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all",title:"Settings",children:e.jsx(Ee,{size:16})})]})]})},dt=["javascript","typescript","jsx","tsx","json","xml","markdown","shell"],lt=["github-dark"],ut=Ne({themes:lt,langs:dt});let G=null;async function mt(){return G||(G=await ut),G}function he(r){return["javascript","typescript","jsx","tsx","json","xml","markdown","shell"].includes(r.toLowerCase())}function xt(r){const a={js:"javascript",ts:"typescript",bash:"shell",sh:"shell",zsh:"shell",fish:"shell",py:"javascript",rb:"javascript",yml:"json",yaml:"json",htm:"json",html:"json",css:"json",sass:"json",scss:"json"},t=r.toLowerCase();return a[t]?a[t]:he(t)?t:"javascript"}const bt=({content:r})=>{const[a,t]=m.useState(null),[s,o]=m.useState({});m.useEffect(()=>{mt().then(t)},[]);const n=u=>{o(d=>({...d,[u]:{...d[u],isCollapsed:!d[u]?.isCollapsed}}))},i=async(u,d)=>{try{if(d==="javascript"||d==="jsx"){const l=await T(`
          try {
            ${u}
          } catch (error) {
            return "Error: " + error.toString();
          }
        `);console.log("Code execution result:",l)}else await navigator.clipboard.writeText(u),console.log("Code copied to clipboard for manual execution")}catch(l){console.error("Failed to execute code:",l)}};if(!a)return e.jsx("pre",{className:"whitespace-pre-wrap",children:r});const c=r.split(/(```[\s\S]*?```)/g);return e.jsx(e.Fragment,{children:c.map((u,d)=>{if(u.startsWith("```")){const l=u.split(`
`),f=l[0].replace("```","").trim(),g=l.slice(1,-1).join(`
`),y=he(f)?f:xt(f),p=`code-block-${d}`,b=s[p]?.isCollapsed||!1,x=["javascript","jsx","typescript","tsx"].includes(y);return e.jsxs("div",{className:"relative border border-adobe-border rounded-lg overflow-hidden bg-adobe-bg-secondary",children:[e.jsxs("div",{className:"flex items-center justify-between px-3 py-2 bg-adobe-bg-tertiary border-b border-adobe-border",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-xs font-mono text-adobe-text-secondary uppercase",children:f||"code"}),e.jsxs("span",{className:"text-xs text-adobe-text-secondary",children:[g.split(`
`).length," lines"]})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("button",{title:"Copy to clipboard",onClick:()=>navigator.clipboard.writeText(g),className:"p-1.5 hover:bg-adobe-bg-primary/80 rounded text-xs transition-colors",children:e.jsx(Ce,{size:14})}),x&&e.jsx("button",{title:"Run in terminal",onClick:()=>i(g,y),className:"p-1.5 hover:bg-adobe-bg-primary/80 rounded text-xs transition-colors text-adobe-accent",children:e.jsx(ke,{size:14})}),e.jsx("button",{title:"Save to file",className:"p-1.5 hover:bg-adobe-bg-primary/80 rounded text-xs transition-colors",children:e.jsx(Te,{size:14})}),e.jsx("button",{title:b?"Expand":"Collapse",onClick:()=>n(p),className:"p-1.5 hover:bg-adobe-bg-primary/80 rounded text-xs transition-colors",children:b?e.jsx(M,{size:14}):e.jsx(Re,{size:14})})]})]}),!b&&e.jsx("div",{className:"relative",children:e.jsx("div",{className:"overflow-x-auto",dangerouslySetInnerHTML:{__html:a.codeToHtml(g,{lang:y,theme:"github-dark"})}})}),b&&e.jsx("div",{className:"px-3 py-2 text-adobe-text-secondary text-sm italic",children:"Code block collapsed. Click expand to view."})]},d)}return e.jsx("div",{children:u},d)})})},ht=({message:r})=>{const a=r.role==="user";return e.jsx("div",{className:`flex gap-3 ${a?"justify-end":"justify-start"} mb-4`,children:e.jsx("div",{className:`max-w-[85%] rounded-2xl px-4 py-3 text-sm leading-relaxed shadow-sm ${a?"bg-adobe-bg-tertiary text-adobe-text-primary ml-8 rounded-br-md":"bg-adobe-bg-primary text-adobe-text-primary mr-8 rounded-bl-md border border-adobe-border"}`,children:e.jsx("div",{className:"whitespace-pre-wrap",children:e.jsx(bt,{content:r.content})})})})};function ge(r,a){let t=null,s=0;return(...o)=>{const n=Date.now();n-s>a?(s=n,r(...o)):(t&&clearTimeout(t),t=setTimeout(()=>{s=Date.now(),r(...o),t=null},a-(n-s)))}}const gt=()=>{const{messages:r,isLoading:a,currentSession:t}=ee(),s=m.useRef(null),o=m.useRef(null),[n,i]=m.useState(!1),c=m.useRef();m.useEffect(()=>{const l=o.current,f=s.current;if(!l||!f)return;const{scrollTop:g,scrollHeight:y,clientHeight:p}=l;y-(g+p)<150&&requestAnimationFrame(()=>{f.scrollIntoView({behavior:"smooth"})})},[r.length,a]);const u=m.useCallback(ge(()=>{const l=o.current;if(!l)return;clearTimeout(c.current);const{scrollTop:f,scrollHeight:g,clientHeight:y}=l,p=g-(f+y)<100;i(!p),c.current=setTimeout(()=>{i(!1)},2e3)},100),[]);m.useEffect(()=>{const l=o.current;if(l)return l.addEventListener("scroll",u,{passive:!0}),()=>{l.removeEventListener("scroll",u),clearTimeout(c.current)}},[u]);const d=()=>{s.current?.scrollIntoView({behavior:"smooth"})};return e.jsxs("div",{ref:o,className:`flex-1 overflow-y-auto px-3 py-2 space-y-4
                chat-messages-scrollbar
                relative`,children:[(!t||r.length===0)&&e.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-adobe-text-secondary gap-3",children:[e.jsx("div",{className:"w-20 h-20 mb-2 flex items-center justify-center",children:e.jsx(J,{size:80,className:"text-adobe-text-secondary"})}),e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary",children:"Start a conversation"}),e.jsx("p",{className:"text-sm text-center max-w-md",children:"Type a message below to begin chatting with SahAI"})]}),r.map(l=>e.jsx(ht,{message:l},l.id)),a&&e.jsx("div",{className:"flex items-center gap-2 text-adobe-text-secondary text-sm",children:e.jsx("span",{children:"AI is thinking..."})}),e.jsx("div",{ref:s}),n&&e.jsx("button",{onClick:d,className:"absolute right-4 bottom-4 p-2 rounded-full bg-adobe-bg-tertiary border border-adobe-border text-adobe-text-primary hover:bg-adobe-bg-secondary transition-all duration-300 shadow-md","aria-label":"Scroll to bottom",children:e.jsx(Le,{size:18})})]})},pe=m.forwardRef(({value:r,onChange:a,minHeight:t=72,maxHeight:s=200,onHeightChange:o,className:n="",style:i,...c},u)=>{const d=m.useRef(null),l=m.useRef(t),f=m.useCallback(p=>{d.current=p,typeof u=="function"?u(p):u&&(u.current=p)},[u]),g=m.useCallback(()=>{const p=d.current;if(!p)return t;p.style.height="auto";const b=p.scrollHeight,x=Math.min(Math.max(b,t),s);return p.style.height=`${x}px`,x},[t,s]),y=m.useCallback(p=>{a(p),requestAnimationFrame(()=>{const b=g();b!==l.current&&(l.current=b,o?.(b))})},[a,g,o]);return m.useEffect(()=>{!r&&d.current&&(d.current.style.height=`${t}px`,l.current!==t&&(l.current=t,o?.(t)))},[r,t,o]),m.useEffect(()=>{if(d.current){const p=g();l.current=p,o?.(p)}},[g,o]),e.jsx("textarea",{ref:f,value:r,onChange:y,className:`resize-none transition-all duration-150 ease-out ${n}`,style:{minHeight:`${t}px`,maxHeight:`${s}px`,height:`${t}px`,...i},...c})});pe.displayName="AutoResizeTextarea";const pt=ie.memo(()=>{const[r,a]=m.useState(""),[t,s]=m.useState(!1),o=m.useRef(null),{addMessage:n,isLoading:i,setLoading:c,currentSession:u,createNewSession:d}=ee(),l=4e3,f=!r.trim(),g=r.length>l*.9,y=m.useCallback(j=>{a(j.target.value)},[]),p=m.useCallback(async()=>{const j=r.trim();if(!(!j||i)){a("");try{c(!0),u||d(),n({content:j,role:"user"}),setTimeout(()=>{n({content:`Echo: ${j}`,role:"assistant"}),c(!1)},1e3)}catch{a(j),c(!1)}}},[r,i,u,n,c,d]),b=m.useCallback(j=>{j.key==="Enter"&&!j.shiftKey&&!t&&(j.preventDefault(),p())},[p,t]),x=m.useCallback(()=>{console.log("File attachment clicked - functionality to be implemented")},[]),h=m.useCallback(()=>{console.log("Voice input clicked - functionality to be implemented")},[]);return e.jsxs("div",{className:"px-4 pb-3 pt-2 bg-adobe-bg-secondary border-t border-adobe-border",children:[e.jsxs("div",{className:"relative flex items-center bg-transparent rounded-lg border border-adobe-text-secondary/50 focus-within:border-adobe-accent focus-within:ring-1 focus-within:ring-adobe-accent transition-colors",children:[e.jsx("div",{className:"flex items-center pl-3",children:e.jsx("button",{onClick:x,className:"text-adobe-text-secondary hover:text-adobe-accent transition p-1.5 rounded",title:"Attach file",disabled:i,children:e.jsx(Ie,{size:18})})}),e.jsx(pe,{ref:o,maxLength:l,value:r,onChange:y,onKeyDown:b,onCompositionStart:()=>s(!0),onCompositionEnd:()=>s(!1),placeholder:"Type a message...",disabled:i,minHeight:72,maxHeight:200,className:"flex-1 bg-transparent text-adobe-text-primary text-sm p-3 outline-none placeholder:text-adobe-text-secondary/80 leading-relaxed overflow-y-auto chat-messages-scrollbar"}),e.jsxs("div",{className:"flex items-center pr-3 space-x-1",children:[e.jsx("button",{onClick:h,className:"text-adobe-text-secondary hover:text-adobe-warning transition p-1.5 rounded disabled:opacity-40",title:"Voice input",disabled:i,children:e.jsx(Pe,{size:18})}),e.jsx("button",{onClick:p,disabled:f||i,className:"text-adobe-accent hover:text-adobe-accent-hover transition p-1.5 rounded disabled:text-adobe-text-secondary/50 disabled:hover:text-adobe-text-secondary/50",title:"Send",children:i?e.jsx(Q,{size:18,className:"animate-spin"}):e.jsx(Oe,{size:18})})]})]}),e.jsxs("div",{className:"flex justify-between items-center mt-1 px-1",children:[e.jsxs("span",{className:`text-xs ${g?"text-adobe-warning":"text-adobe-text-secondary"}`,children:[r.length,"/",l]}),e.jsx("span",{className:"text-xs text-adobe-text-secondary",children:"Enter to send, Shift+Enter for new line"})]})]})}),ft=({models:r,value:a,onChange:t,placeholder:s="Search models..."})=>{const[o,n]=m.useState(""),[i,c]=m.useState(!1),[u,d]=m.useState(0),l=m.useRef(null),f=m.useRef(null),g=r.filter(x=>x.name.toLowerCase().includes(o.toLowerCase())||x.id.toLowerCase().includes(o.toLowerCase())),y=r.find(x=>x.id===a),p=m.useCallback(x=>{t(x),c(!1),n(""),d(0)},[t]),b=m.useCallback(x=>{if(!i&&(x.key==="ArrowDown"||x.key==="ArrowUp")){x.preventDefault(),c(!0),n("");return}if(i)switch(x.key){case"ArrowDown":x.preventDefault(),d(h=>h<g.length-1?h+1:h);break;case"ArrowUp":x.preventDefault(),d(h=>h>0?h-1:0);break;case"Enter":x.preventDefault(),g[u]&&p(g[u].id);break;case"Escape":x.preventDefault(),c(!1),d(0);break}},[i,g,u,p]);return m.useEffect(()=>{const x=h=>{l.current&&!l.current.contains(h.target)&&(c(!1),d(0))};return document.addEventListener("mousedown",x),()=>document.removeEventListener("mousedown",x)},[]),m.useEffect(()=>{d(0)},[g]),e.jsxs("div",{className:"relative",ref:l,children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{ref:f,type:"text",value:i?o:y?.name||"",onChange:x=>{n(x.target.value),c(!0),d(0)},onFocus:()=>{c(!0),n(""),d(0)},onKeyDown:b,placeholder:s,className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-3 py-2 text-adobe-text-primary focus-within:border-adobe-accent outline-none pr-10",readOnly:!i}),e.jsx("button",{onClick:()=>{i&&g.length>0?p(g[u]?.id||g[0].id):(c(!0),n(""),d(0))},className:"absolute right-2 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary hover:text-adobe-text-primary",children:e.jsx(ce,{size:18})})]}),i&&e.jsx("div",{className:"absolute z-[9999] mt-1 w-full bg-adobe-bg-secondary border border-adobe-border rounded-md shadow-lg max-h-52 overflow-auto",children:g.length>0?g.map((x,h)=>e.jsx("div",{className:`px-4 py-2 cursor-pointer ${a===x.id?"bg-adobe-accent text-white":h===u?"bg-adobe-bg-tertiary text-adobe-text-primary":"hover:bg-adobe-bg-tertiary text-adobe-text-primary"}`,onClick:()=>p(x.id),onMouseEnter:()=>d(h),children:x.name},x.id)):e.jsx("div",{className:"px-4 py-2 text-adobe-text-secondary",children:"No models found"})})]})},yt=({providers:r,value:a,onChange:t,placeholder:s="Search providers..."})=>{const[o,n]=m.useState(""),[i,c]=m.useState(!1),[u,d]=m.useState(0),l=m.useRef(null),f=m.useRef(null),g=r.filter(x=>x.name.toLowerCase().includes(o.toLowerCase())||x.id.toLowerCase().includes(o.toLowerCase())),y=r.find(x=>x.id===a),p=m.useCallback(x=>{t(x),c(!1),n(""),d(0)},[t]),b=m.useCallback(x=>{if(!i&&(x.key==="ArrowDown"||x.key==="ArrowUp")){x.preventDefault(),c(!0),n("");return}if(i)switch(x.key){case"ArrowDown":x.preventDefault(),d(h=>h<g.length-1?h+1:h);break;case"ArrowUp":x.preventDefault(),d(h=>h>0?h-1:0);break;case"Enter":x.preventDefault(),g[u]&&p(g[u].id);break;case"Escape":x.preventDefault(),c(!1),d(0);break}},[i,g,u,p]);return m.useEffect(()=>{const x=h=>{l.current&&!l.current.contains(h.target)&&(c(!1),d(0))};return document.addEventListener("mousedown",x),()=>document.removeEventListener("mousedown",x)},[]),m.useEffect(()=>{d(0)},[g]),e.jsxs("div",{className:"relative",ref:l,children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{ref:f,type:"text",value:i?o:y?.name||"",onChange:x=>{n(x.target.value),c(!0),d(0)},onFocus:()=>{c(!0),n(""),d(0)},onKeyDown:b,placeholder:s,className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-lg px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-2 focus:ring-adobe-accent/20 outline-none transition-all pr-10",readOnly:!i}),e.jsx("button",{onClick:()=>{i&&g.length>0?p(g[u]?.id||g[0].id):(c(!0),n(""),d(0))},className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(M,{size:18,className:`transition-transform ${i?"rotate-180":""}`})})]}),i&&e.jsx("div",{className:"absolute z-[9999] mt-1 w-full bg-adobe-bg-secondary border border-adobe-border rounded-lg shadow-lg max-h-52 overflow-auto",children:g.length>0?g.map((x,h)=>e.jsxs("div",{className:`px-4 py-3 cursor-pointer flex items-center justify-between ${a===x.id?"bg-adobe-accent text-white":h===u?"bg-adobe-bg-tertiary text-adobe-text-primary":"hover:bg-adobe-bg-tertiary text-adobe-text-primary"}`,onClick:()=>p(x.id),onMouseEnter:()=>d(h),children:[e.jsx("span",{children:x.name}),x.isConfigured&&e.jsx("div",{className:`w-2 h-2 rounded-full ${a===x.id?"bg-white":"bg-green-500"}`})]},x.id)):e.jsx("div",{className:"px-4 py-3 text-adobe-text-secondary",children:"No providers found"})})]})};class O extends m.Component{constructor(){super(...arguments);P(this,"state",{hasError:!1})}static getDerivedStateFromError(t){return{hasError:!0,error:t}}componentDidCatch(t,s){console.error("Uncaught error:",t,s)}render(){return this.state.hasError?this.props.fallback?this.props.fallback:e.jsxs("div",{className:"flex flex-col items-center justify-center p-8 bg-adobe-bg-primary text-adobe-text-primary",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Something went wrong"}),e.jsx("p",{className:"text-adobe-text-secondary mb-4",children:this.state.error?.message||"An unexpected error occurred"}),e.jsx("button",{className:"px-4 py-2 bg-adobe-accent text-white rounded-md hover:bg-adobe-accent/90",onClick:()=>this.setState({hasError:!1,error:void 0}),children:"Try again"})]}):this.props.children}}class vt extends m.Component{constructor(){super(...arguments);P(this,"retryTimeout");P(this,"state",{hasError:!1,errorCount:0});P(this,"handleRetry",()=>{this.setState({hasError:!1,error:void 0}),this.props.onRetry&&this.props.onRetry()})}static getDerivedStateFromError(t){return{hasError:!0,error:t}}componentDidCatch(t,s){if(console.error(`Provider ${this.props.providerId} error:`,t,s),this.setState(o=>({errorCount:o.errorCount+1})),this.state.errorCount>=3){console.warn(`Provider ${this.props.providerId} has failed ${this.state.errorCount} times, stopping auto-retry`);return}this.state.errorCount<2&&this.props.providerId!=="ollama"&&(this.retryTimeout=setTimeout(()=>{this.handleRetry()},2e3*this.state.errorCount))}componentWillUnmount(){this.retryTimeout&&window.clearTimeout(this.retryTimeout)}render(){if(this.state.hasError){const t=this.state.errorCount>=3;return e.jsxs("div",{className:"flex flex-col items-center justify-center p-6 bg-adobe-bg-secondary border border-adobe-border rounded-lg",children:[e.jsx(de,{size:32,className:"text-adobe-warning mb-3"}),e.jsx("h3",{className:"text-lg font-semibold text-adobe-text-primary mb-2",children:"Provider Error"}),e.jsx("p",{className:"text-adobe-text-secondary text-sm text-center mb-4",children:t?`${this.props.providerId} has encountered multiple errors. Please check your configuration.`:`There was an error loading ${this.props.providerId}. ${this.state.errorCount<2?"Retrying automatically...":""}`}),this.state.error&&e.jsxs("details",{className:"mb-4 w-full",children:[e.jsx("summary",{className:"text-xs text-adobe-text-secondary cursor-pointer hover:text-adobe-text-primary",children:"Error Details"}),e.jsx("pre",{className:"text-xs text-adobe-error mt-2 p-2 bg-adobe-bg-primary rounded border overflow-auto max-h-20",children:this.state.error.message})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("button",{onClick:this.handleRetry,className:"flex items-center gap-2 px-3 py-1.5 bg-adobe-accent text-white rounded text-sm hover:bg-adobe-accent/90 transition-colors",children:[e.jsx(X,{size:14}),"Try Again"]}),t&&e.jsx("button",{onClick:()=>this.setState({hasError:!1,error:void 0,errorCount:0}),className:"px-3 py-1.5 border border-adobe-border text-adobe-text-primary rounded text-sm hover:bg-adobe-bg-tertiary transition-colors",children:"Reset"})]}),e.jsxs("p",{className:"text-xs text-adobe-text-secondary mt-3 text-center",children:["Error count: ",this.state.errorCount,"/3"]})]})}return this.props.children}}const jt=()=>{const{closeModal:r}=$(),{providers:a,activeProviderId:t,saveProviderSelection:s,loadModelsForProvider:o,updateProviderConfig:n,setSelectedModel:i}=D(),[c,u]=m.useState(t||""),[d,l]=m.useState(""),[f,g]=m.useState(""),[y,p]=m.useState(""),b=a.find(S=>S.id===c);m.useEffect(()=>{b&&(l(b.apiKey||""),g(b.baseURL||""),p(b.selectedModelId||""))},[b]);const x=S=>{u(S);const k=a.find(fe=>fe.id===S);k&&(l(k.apiKey||""),g(k.baseURL||""),p(k.selectedModelId||""))},h=S=>{b&&(b.configType==="apiKey"?l(S):g(S))},j=m.useCallback(ge(()=>{if(!c||!b||c==="ollama")return;(b.configType==="apiKey"?!!d.trim():!!f.trim())&&o(c)},1e3),[c,b,d,f]);m.useEffect(()=>{if(!b)return;if(b.configType==="apiKey"&&d!==(b.apiKey||"")||b.configType==="baseURL"&&f!==(b.baseURL||"")||y!==(b.selectedModelId||"")){const k={};b.configType==="apiKey"?k.apiKey=d:k.baseURL=f,n(c,k),y&&y!==b.selectedModelId&&i(c,y)}j()},[d,f,y,b,c]);const v=()=>{if(c){if(b?.configType==="apiKey"&&!d.trim()){R.error("Configuration Error","Please enter your API key first");return}if(b?.configType==="baseURL"&&!f.trim()){R.error("Configuration Error","Please enter the base URL first");return}o(c)}},N=()=>{if(!c||!b)return;const S={selectedModelId:y};if(b.configType==="apiKey"?S.apiKey=d:S.baseURL=f,!(b.configType==="apiKey"?!!S.apiKey:!!S.baseURL)){R.error("Configuration Error",`Please enter your ${b.configType==="apiKey"?"API key":"base URL"}`);return}if(!S.selectedModelId){R.error("Configuration Error","Please select a model");return}s(c,S),r()},w=b?.models.map(S=>({id:S.id,name:S.name}))||[],E=a.map(S=>({id:S.id,name:S.name,isConfigured:S.isConfigured}));return e.jsx(O,{children:e.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:e.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-xl w-[600px] shadow-2xl",children:[e.jsx("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-6 rounded-t-xl",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-adobe-text-primary",children:"Configure AI Provider"}),e.jsx("p",{className:"text-sm text-adobe-text-secondary mt-1",children:"Select and configure your AI provider"})]}),e.jsx("button",{onClick:r,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors p-1 hover:bg-adobe-bg-tertiary rounded-lg",children:e.jsx(_,{size:20})})]})}),e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Choose Provider"}),e.jsx(yt,{providers:E,value:c,onChange:x,placeholder:"Search and select a provider..."})]}),b&&e.jsxs(vt,{providerId:b.id,onRetry:v,children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:b.configType==="apiKey"?"API Key":"Base URL"}),e.jsx("input",{type:b.configType==="apiKey"?"password":"text",placeholder:b.configType==="apiKey"?`Enter your ${b.name} API key...`:`Enter ${b.name} base URL...`,value:b.configType==="apiKey"?d:f,onChange:S=>h(S.target.value),className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-lg px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-2 focus:ring-adobe-accent/20 outline-none transition-all"})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary",children:"Select Model"}),b.id==="ollama"&&e.jsx("button",{onClick:v,disabled:b.isLoading,className:"text-xs text-adobe-accent hover:text-adobe-accent/80 disabled:opacity-50",children:b.isLoading?"Loading...":"Load Models"})]}),b.isLoading?e.jsxs("div",{className:"flex items-center space-x-2 text-adobe-text-secondary p-4 bg-adobe-bg-secondary rounded-lg border border-adobe-border",children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-adobe-accent"}),e.jsx("span",{children:"Loading models..."})]}):b.error?e.jsxs("div",{className:"p-4 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400 text-sm",children:[b.error,e.jsx("button",{onClick:v,className:"ml-2 text-red-300 hover:text-red-200 underline",children:"Retry"})]}):w.length>0?e.jsx(ft,{models:w,value:y,onChange:p,placeholder:`Search ${b.name} models...`}):e.jsx("div",{className:"p-4 bg-adobe-bg-secondary rounded-lg border border-adobe-border text-adobe-text-secondary text-sm",children:b.id==="ollama"?'Click "Load Models" to fetch available Ollama models':"No models available. Configure the provider first."})]})]})]}),b&&e.jsx("div",{className:"border-t border-adobe-border p-6 bg-adobe-bg-secondary rounded-b-xl",children:e.jsxs("div",{className:"flex justify-end space-x-3",children:[e.jsx("button",{onClick:r,className:"px-4 py-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-lg transition-all",children:"Cancel"}),e.jsx("button",{onClick:N,disabled:!c||!b||b.configType==="apiKey"&&!d.trim()||b.configType==="baseURL"&&!f.trim()||!y,className:"px-6 py-2 bg-adobe-accent text-white rounded-lg hover:bg-adobe-accent/90 transition-all disabled:opacity-50 disabled:cursor-not-allowed font-medium shadow-sm",children:"Save & Configure"})]})})]})})})},te=z((r,a)=>({sessions:[],currentSessionId:null,isLoading:!1,error:null,loadHistory:async()=>{r({isLoading:!0,error:null});try{if(!window.CSInterface){const o=localStorage.getItem(C),n=o?JSON.parse(o):[];r({sessions:n,isLoading:!1});return}const s=await T("loadHistory()");if(s&&s.success&&s.data){const o=Array.isArray(s.data)?s.data:[];r({sessions:o,isLoading:!1})}else if(s&&s.success)r({sessions:[],isLoading:!1});else throw new Error(s?.message||"Failed to load history from ExtendScript")}catch(t){console.error("Failed to load history:",t);try{const s=localStorage.getItem(C),o=s?JSON.parse(s):[];r({sessions:o,isLoading:!1,error:`Using local storage fallback: ${t.message}`})}catch{r({error:t.message||"Failed to load chat history",isLoading:!1,sessions:[]})}}},saveSession:async t=>{try{r(n=>({sessions:n.sessions.some(i=>i.id===t.id)?n.sessions.map(i=>i.id===t.id?t:i):[...n.sessions,t]}));const s=a().sessions;if(!window.CSInterface){localStorage.setItem(C,JSON.stringify(s));return}await T(`saveHistory(${JSON.stringify(s)})`),localStorage.setItem(C,JSON.stringify(s))}catch(s){console.error("Failed to save session:",s);try{const o=a().sessions;localStorage.setItem(C,JSON.stringify(o))}catch{r({error:s.message||"Failed to save session"})}}},deleteSession:async t=>{try{r(n=>({sessions:n.sessions.filter(i=>i.id!==t),currentSessionId:n.currentSessionId===t?null:n.currentSessionId}));const s=a().sessions;if(!window.CSInterface){localStorage.setItem(C,JSON.stringify(s));return}await T(`saveHistory(${JSON.stringify(s)})`),localStorage.setItem(C,JSON.stringify(s))}catch(s){console.error("Failed to delete session:",s);try{const o=a().sessions;localStorage.setItem(C,JSON.stringify(o))}catch{r({error:s.message||"Failed to delete session"})}}},clearHistory:async()=>{try{if(r({sessions:[],currentSessionId:null}),!window.CSInterface){localStorage.setItem(C,JSON.stringify([]));return}await T("saveHistory([])"),localStorage.setItem(C,JSON.stringify([]))}catch(t){console.error("Failed to clear history:",t);try{localStorage.setItem(C,JSON.stringify([]))}catch{r({error:t.message||"Failed to clear history"})}}},createSession:t=>{const s={id:crypto.randomUUID(),title:t||`Chat ${new Date().toLocaleDateString()}`,messages:[],createdAt:Date.now(),updatedAt:Date.now()};r(n=>({sessions:[s,...n.sessions],currentSessionId:s.id}));const{saveSession:o}=a();return o(s),s},updateSession:(t,s)=>{r(o=>({sessions:o.sessions.map(n=>n.id===t?{...n,...s,updatedAt:Date.now()}:n)}))},setCurrentSession:t=>{r({currentSessionId:t})},getCurrentSession:()=>{const{sessions:t,currentSessionId:s}=a();return t.find(o=>o.id===s)||null},getSessionById:t=>{const{sessions:s}=a();return s.find(o=>o.id===t)||null},getSortedSessions:()=>{const{sessions:t}=a();return[...t].sort((s,o)=>o.updatedAt-s.updatedAt)}})),ne=Object.freeze(Object.defineProperty({__proto__:null,useHistoryStore:te},Symbol.toStringTag,{value:"Module"})),Nt=()=>{const{closeModal:r}=$(),{sessions:a}=te(),[t,s]=m.useState({theme:"auto",autoSave:!0,showNotifications:!0,maxHistoryItems:100,debugMode:!1}),[o,n]=m.useState("settings"),[i,c]=m.useState(!1),[u,d]=m.useState(!0),[l,f]=m.useState("30d"),[g,y]=m.useState(!1);m.useEffect(()=>{(async()=>{try{const N=await I.load();N&&typeof N=="object"&&s(N)}catch(N){console.error("Failed to load settings:",N)}finally{d(!1)}})()},[]);const p=[{id:"settings",title:"General Settings",description:"Configure application preferences",icon:e.jsx(se,{size:16,className:"text-adobe-accent"})},{id:"analytics",title:"Analytics",description:"View usage statistics",icon:e.jsx(Ae,{size:16,className:"text-adobe-accent"})},{id:"help",title:"Help & Support",description:"Get help and answers",icon:e.jsx(Me,{size:16,className:"text-adobe-accent"})},{id:"about",title:"About",description:"About SahAI Extension",icon:e.jsx(V,{size:16,className:"text-adobe-accent"})}],x=(()=>{const v=Date.now(),N=a.filter(w=>{if(l==="all")return!0;const E=parseInt(l.replace("d",""));return v-w.createdAt<=E*24*60*60*1e3});return{messages:N.reduce((w,E)=>w+E.messages.length,0),sessions:N.length,tokens:N.reduce((w,E)=>w+(E.tokenCount||0),0),cost:N.reduce((w,E)=>w+(E.cost||0),0),avgLatency:N.length>0?N.reduce((w,E)=>w+(E.avgLatency||0),0)/N.length:0}})(),h=()=>{y(!0),setTimeout(()=>y(!1),800)},j=async()=>{c(!0);try{const N={...await I.load(),appSettings:t};await I.save(N),r()}catch(v){console.error("Failed to save settings:",v)}finally{c(!1)}};return e.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:e.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-lg w-[700px] h-[600px] shadow-2xl flex flex-col",children:[e.jsx("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-lg font-semibold text-adobe-text-primary",children:"Settings"}),e.jsx("button",{onClick:r,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(_,{size:20})})]})}),e.jsxs("div",{className:"flex-1 flex overflow-hidden",children:[e.jsx("div",{className:"w-1/3 border-r border-adobe-border bg-adobe-bg-secondary p-4 overflow-y-auto",children:e.jsx("div",{className:"space-y-1",children:p.map(v=>e.jsx("button",{onClick:()=>n(v.id),className:`w-full text-left p-3 rounded-md transition-colors ${o===v.id?"bg-adobe-accent/10 text-adobe-text-primary":"text-adobe-text-secondary hover:bg-adobe-bg-tertiary"}`,children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-1.5 rounded-md bg-adobe-bg-tertiary",children:v.icon}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-sm",children:v.title}),e.jsx("div",{className:"text-xs mt-1",children:v.description})]})]})},v.id))})}),e.jsx("div",{className:"w-2/3 p-6 overflow-y-auto",children:u?e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-adobe-accent"})}):o==="settings"?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-adobe-text-primary mb-3",children:"Appearance"}),e.jsx("div",{className:"space-y-3",children:e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-adobe-text-secondary mb-2",children:"Theme"}),e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:t.theme,onChange:v=>s(N=>({...N,theme:v.target.value})),className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-2 pr-8 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none appearance-none",children:[e.jsx("option",{value:"auto",children:"Auto (System)"}),e.jsx("option",{value:"light",children:"Light"}),e.jsx("option",{value:"dark",children:"Dark"})]}),e.jsx(M,{size:16,className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary pointer-events-none"})]})]})})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-adobe-text-primary mb-3",children:"General"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("label",{className:"flex items-center gap-3",children:[e.jsx("input",{type:"checkbox",checked:t.autoSave,onChange:v=>s(N=>({...N,autoSave:v.target.checked})),className:"w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"}),e.jsx("span",{className:"text-sm text-adobe-text-primary",children:"Auto-save conversations"})]}),e.jsxs("label",{className:"flex items-center gap-3",children:[e.jsx("input",{type:"checkbox",checked:t.showNotifications,onChange:v=>s(N=>({...N,showNotifications:v.target.checked})),className:"w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"}),e.jsx("span",{className:"text-sm text-adobe-text-primary",children:"Show notifications"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm text-adobe-text-secondary mb-2",children:["Max history items (",t.maxHistoryItems,")"]}),e.jsx("input",{type:"range",min:"10",max:"500",step:"10",value:t.maxHistoryItems,onChange:v=>s(N=>({...N,maxHistoryItems:parseInt(v.target.value)})),className:"w-full h-2 bg-adobe-bg-secondary rounded-lg appearance-none cursor-pointer"})]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-adobe-text-primary mb-3",children:"Advanced"}),e.jsx("div",{className:"space-y-3",children:e.jsxs("label",{className:"flex items-center gap-3",children:[e.jsx("input",{type:"checkbox",checked:t.debugMode,onChange:v=>s(N=>({...N,debugMode:v.target.checked})),className:"w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"}),e.jsx("span",{className:"text-sm text-adobe-text-primary",children:"Debug mode"})]})})]}),e.jsx("div",{className:"pt-4",children:e.jsxs("button",{onClick:j,disabled:i,className:"flex items-center gap-2 px-6 py-2 bg-adobe-accent text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-adobe-accent/90 transition-colors",children:[e.jsx(se,{size:16}),e.jsx("span",{children:i?"Saving...":"Save Settings"})]})})]}):o==="analytics"?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary",children:"Analytics"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:l,onChange:v=>f(v.target.value),className:"appearance-none bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-1.5 pr-8 text-sm text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none cursor-pointer",children:[e.jsx("option",{value:"7d",children:"Last 7 days"}),e.jsx("option",{value:"30d",children:"Last 30 days"}),e.jsx("option",{value:"90d",children:"Last 90 days"}),e.jsx("option",{value:"all",children:"All time"})]}),e.jsx(M,{size:14,className:"absolute right-2 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary pointer-events-none"})]}),e.jsx("button",{onClick:h,disabled:g,className:"p-1.5 text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(X,{size:16,className:g?"animate-spin":""})})]})]}),g?e.jsx("div",{className:"grid grid-cols-2 gap-4",children:[...Array(4)].map((v,N)=>e.jsx("div",{className:"h-24 bg-adobe-bg-secondary rounded-md animate-pulse"},N))}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-1",children:"Messages"}),e.jsx("div",{className:"text-2xl font-medium text-adobe-text-primary",children:x.messages.toLocaleString()})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-1",children:"Sessions"}),e.jsx("div",{className:"text-2xl font-medium text-adobe-text-primary",children:x.sessions.toLocaleString()})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-1",children:"Tokens Used"}),e.jsx("div",{className:"text-2xl font-medium text-adobe-text-primary",children:x.tokens.toLocaleString()})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-1",children:"Est. Cost"}),e.jsxs("div",{className:"text-2xl font-medium text-adobe-text-primary",children:["$",x.cost.toFixed(4)]})]})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary",children:"Average Latency"}),e.jsxs("div",{className:"text-sm font-medium text-adobe-text-primary",children:[x.avgLatency.toFixed(2)," seconds"]})]}),e.jsx("div",{className:"h-2 bg-adobe-bg-tertiary rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-adobe-accent transition-all duration-300",style:{width:`${Math.min(100,x.avgLatency*50)}%`}})})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("h4",{className:"text-sm font-medium text-adobe-text-primary mb-2",children:"Performance Tips"}),e.jsxs("ul",{className:"text-sm text-adobe-text-secondary space-y-1",children:[e.jsx("li",{children:"• Use concise prompts to reduce token usage"}),e.jsx("li",{children:"• Select faster models for simple tasks"}),e.jsx("li",{children:"• Monitor usage to optimize costs"})]})]})]})]}):o==="help"?e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary mb-2",children:"Help & Support"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"p-4 bg-adobe-bg-secondary rounded-md",children:[e.jsx("h4",{className:"font-medium text-adobe-text-primary mb-2",children:"Documentation"}),e.jsx("p",{className:"text-sm text-adobe-text-secondary",children:"Read our comprehensive documentation for detailed guides."})]}),e.jsxs("div",{className:"p-4 bg-adobe-bg-secondary rounded-md",children:[e.jsx("h4",{className:"font-medium text-adobe-text-primary mb-2",children:"FAQ"}),e.jsx("p",{className:"text-sm text-adobe-text-secondary",children:"Find answers to frequently asked questions."})]}),e.jsxs("div",{className:"p-4 bg-adobe-bg-secondary rounded-md",children:[e.jsx("h4",{className:"font-medium text-adobe-text-primary mb-2",children:"Contact Support"}),e.jsx("p",{className:"text-sm text-adobe-text-secondary",children:"Email <NAME_EMAIL> for assistance."})]})]})]}):e.jsx("div",{className:"flex flex-col items-center justify-center h-full text-adobe-text-secondary",children:e.jsxs("div",{className:"text-center",children:[e.jsx(V,{size:48,className:"mx-auto mb-4 opacity-50"}),e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary mb-2",children:"About SahAI"}),e.jsx("p",{className:"text-sm mb-4",children:"Version 2.0.0"}),e.jsx("p",{className:"text-sm",children:"AI-powered assistant for Adobe Creative Suite"})]})})})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary border-t border-adobe-border p-3 text-xs text-adobe-text-secondary text-center",children:[o==="settings"&&"General Settings",o==="analytics"&&"Usage Analytics",o==="help"&&"Help & Support",o==="about"&&"About SahAI"]})]})})},St=()=>{const{closeModal:r}=$(),{sessions:a,isLoading:t,error:s,loadHistory:o,deleteSession:n,getSortedSessions:i}=te(),[c,u]=m.useState(""),[d,l]=m.useState(null),[f,g]=m.useState("recent");m.useEffect(()=>{o()},[o]);const y=i().filter(h=>h.title.toLowerCase().includes(c.toLowerCase())||h.messages.some(j=>j.content.toLowerCase().includes(c.toLowerCase()))).sort((h,j)=>f==="alphabetical"?h.title.localeCompare(j.title):f==="oldest"?h.createdAt-j.createdAt:j.createdAt-h.createdAt),p=async(h,j)=>{j.stopPropagation(),confirm("Are you sure you want to delete this chat session?")&&await n(h)},b=h=>{const j=new Date(h),N=(new Date().getTime()-j.getTime())/(1e3*60*60);return N<24?j.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):N<24*7?j.toLocaleDateString([],{weekday:"short",hour:"2-digit",minute:"2-digit"}):j.toLocaleDateString([],{month:"short",day:"numeric",year:"numeric"})},x=h=>{const j=h.messages[h.messages.length-1];if(!j)return"No messages";const v=j.content.slice(0,100);return v.length<j.content.length?`${v}...`:v};return e.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:e.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-lg w-[700px] h-[600px] shadow-2xl flex flex-col",children:[e.jsx("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-lg font-semibold text-adobe-text-primary",children:"Chat History"}),e.jsx("button",{onClick:r,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(_,{size:20})})]})}),e.jsx("div",{className:"p-4 border-b border-adobe-border",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx("input",{type:"text",value:c,onChange:h=>u(h.target.value),placeholder:"Search chat history...",className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-2 pr-10 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none text-sm"}),e.jsx("button",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary hover:text-adobe-text-primary",onClick:()=>u(""),children:c?e.jsx(_,{size:16}):e.jsx(ce,{size:16})})]}),e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:f,onChange:h=>g(h.target.value),className:"appearance-none bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-2 pl-3 pr-8 text-adobe-text-primary text-sm focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none cursor-pointer",children:[e.jsx("option",{value:"recent",children:"Most Recent"}),e.jsx("option",{value:"oldest",children:"Oldest First"}),e.jsx("option",{value:"alphabetical",children:"Alphabetical"})]}),e.jsx(M,{size:16,className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary pointer-events-none"})]})]})}),e.jsx("div",{className:"flex-1 overflow-hidden",children:t?e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-adobe-accent"})}):s?e.jsxs("div",{className:"p-4 text-center",children:[e.jsxs("div",{className:"bg-red-900/20 border border-red-800/50 rounded-lg p-4 mb-4",children:[e.jsx("p",{className:"text-red-400 font-medium mb-2",children:"Error loading history:"}),e.jsx("p",{className:"text-sm text-red-300",children:s})]}),e.jsx("button",{onClick:o,className:"px-4 py-2 bg-adobe-accent hover:bg-adobe-accent-hover text-white rounded-md transition-colors",children:"Retry Loading History"})]}):y.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-adobe-text-secondary gap-2",children:[e.jsx(J,{size:48,className:"opacity-50"}),e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary",children:c?"No matching sessions found":"No chat history yet"}),e.jsx("p",{className:"text-sm",children:c?"Try a different search term":"Start a new conversation to see it here"})]}):e.jsx("div",{className:"h-full overflow-y-auto p-2 space-y-2",children:y.map(h=>e.jsxs("div",{className:`p-3 rounded-md cursor-pointer transition-colors ${d?.id===h.id?"bg-adobe-accent/10 border-l-2 border-adobe-accent":"bg-adobe-bg-secondary hover:bg-adobe-bg-tertiary"}`,onClick:()=>l(h),children:[e.jsxs("div",{className:"flex justify-between items-start gap-2",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"font-medium text-adobe-text-primary truncate",children:h.title}),e.jsx("p",{className:"text-sm text-adobe-text-secondary mt-1 line-clamp-2",children:x(h)})]}),e.jsx("button",{onClick:j=>p(h.id,j),className:"text-adobe-text-secondary hover:text-adobe-error transition-colors p-1",title:"Delete session",children:e.jsx(_e,{size:14})})]}),e.jsxs("div",{className:"flex justify-between items-center mt-2",children:[e.jsxs("div",{className:"flex items-center gap-1 text-xs text-adobe-text-secondary",children:[e.jsx(J,{size:12}),e.jsxs("span",{children:[h.messages.length," messages"]})]}),e.jsxs("div",{className:"flex items-center gap-1 text-xs text-adobe-text-secondary",children:[e.jsx(De,{size:12}),e.jsx("span",{children:b(h.createdAt)})]})]})]},h.id))})}),e.jsxs("div",{className:"p-3 border-t border-adobe-border text-xs text-adobe-text-secondary text-center",children:["Showing ",y.length," of ",a.length," chat sessions"]})]})})},wt=()=>{const{closeModal:r}=$(),{getActiveProvider:a}=D(),[t,s]=m.useState({isOnline:null,isChecking:!1}),o=a(),n=async()=>{if(!o?.isConfigured){s({isOnline:null,isChecking:!1});return}s(l=>({...l,isChecking:!0,error:void 0}));try{const l=await Z.checkProviderStatus(o.id,{apiKey:o.apiKey,baseURL:o.baseURL});s({isOnline:l.isOnline,latency:l.latency,isChecking:!1,lastChecked:Date.now()})}catch(l){s({isOnline:!1,isChecking:!1,error:l.message,lastChecked:Date.now()})}};m.useEffect(()=>{n();const l=setInterval(n,3e4);return()=>clearInterval(l)},[o]);const i=()=>t.isChecking?e.jsx(Q,{size:20,className:"animate-spin text-yellow-500"}):t.isOnline===!0?e.jsx($e,{size:20,className:"text-green-500"}):t.isOnline===!1?e.jsx(Ue,{size:20,className:"text-red-500"}):e.jsx(W,{size:20,className:"text-gray-500"}),c=()=>t.isChecking?"Checking connection...":t.isOnline===!0?"Online":t.isOnline===!1?"Offline":"Unknown",u=()=>t.isChecking?"text-yellow-600":t.isOnline===!0?"text-green-600":t.isOnline===!1?"text-red-600":"text-gray-600",d=()=>t.isOnline===!0?"good":t.isOnline===!1?"critical":"warning";return e.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:e.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-lg w-[700px] h-[600px] shadow-2xl flex flex-col",children:[e.jsx("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-lg font-semibold text-adobe-text-primary",children:"Provider Status"}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("button",{onClick:()=>n(),disabled:t.isChecking,className:"p-1 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded transition-colors disabled:opacity-50",title:"Refresh status",children:e.jsx(X,{size:18,className:t.isChecking?"animate-spin":""})}),e.jsx("button",{onClick:r,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(_,{size:20})})]})]})}),e.jsx("div",{className:"flex-1 overflow-hidden p-4",children:o?e.jsxs("div",{className:"h-full flex flex-col gap-4",children:[e.jsx("div",{className:"bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex-shrink-0",children:i()}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-adobe-text-primary",children:o.name}),e.jsx("p",{className:`text-sm font-medium ${u()}`,children:c()})]})]}),e.jsx("div",{className:`text-xs px-2 py-1 rounded ${d()==="good"?"bg-green-900/30 text-green-500":d()==="warning"?"bg-yellow-900/30 text-yellow-500":"bg-red-900/30 text-red-500"}`,children:c()})]})}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-adobe-text-secondary",children:"Latency"}),e.jsx("span",{className:"text-xs text-adobe-text-tertiary",children:"Lower is better"})]}),e.jsx("div",{className:"mt-2",children:t.latency?e.jsxs("div",{className:"flex items-end gap-2",children:[e.jsxs("span",{className:"text-2xl font-medium text-adobe-text-primary",children:[t.latency,"ms"]}),e.jsx("span",{className:`text-xs mb-1 ${t.latency<100?"text-green-500":t.latency<300?"text-yellow-500":"text-red-500"}`,children:t.latency<100?"Excellent":t.latency<300?"Good":"Poor"})]}):e.jsx("span",{className:"text-adobe-text-tertiary",children:"--"})})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary",children:"Last Checked"}),e.jsx("div",{className:"mt-2",children:t.lastChecked?e.jsxs("div",{className:"text-adobe-text-primary",children:[e.jsx("div",{className:"text-xl font-medium",children:new Date(t.lastChecked).toLocaleTimeString()}),e.jsx("div",{className:"text-xs text-adobe-text-tertiary mt-1",children:new Date(t.lastChecked).toLocaleDateString()})]}):e.jsx("span",{className:"text-adobe-text-tertiary",children:"--"})})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border col-span-2",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary",children:"Endpoint"}),e.jsx("div",{className:"mt-2",children:o.baseURL?e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"truncate text-adobe-text-primary font-mono text-sm",children:o.baseURL}),e.jsx("button",{className:"text-xs text-adobe-accent hover:text-adobe-accent-hover",onClick:()=>navigator.clipboard.writeText(o.baseURL||""),children:"Copy"})]}):e.jsx("span",{className:"text-adobe-text-tertiary",children:"Not configured"})})]})]}),e.jsxs("div",{className:"flex-1 bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border overflow-auto",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-2",children:"Status Details"}),t.error?e.jsxs("div",{className:"p-3 bg-red-900/20 border border-red-800/50 rounded text-sm text-red-400",children:[e.jsx("div",{className:"font-medium mb-1",children:"Error:"}),e.jsx("div",{children:t.error})]}):e.jsx("div",{className:"text-sm text-adobe-text-primary",children:t.isChecking?"Checking provider status...":t.isOnline===!0?"Provider is online and responding normally.":t.isOnline===!1?"Provider is offline or not responding to requests.":"Provider status unknown. Please check configuration."})]})]}):e.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-adobe-text-secondary gap-2",children:[e.jsx(W,{size:48,className:"opacity-50"}),e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary",children:"No provider selected"}),e.jsx("p",{className:"text-sm",children:"Select a provider to check connection status"})]})}),e.jsx("div",{className:"p-3 border-t border-adobe-border text-xs text-adobe-text-secondary text-center bg-adobe-bg-secondary",children:"Status checks are performed automatically every 30 seconds"})]})})},Et=()=>{const{modal:r}=$();if(!r)return null;switch(r){case"provider":return e.jsx(jt,{});case"settings":return e.jsx(Nt,{});case"chat-history":return e.jsx(St,{});case"status":return e.jsx(wt,{});default:return null}},Ct=({toast:r})=>{const{removeToast:a}=L(),[t,s]=m.useState(!1),o=()=>{s(!0),setTimeout(()=>a(r.id),200)};m.useEffect(()=>{const c=setTimeout(()=>{o()},r.duration||4e3);return()=>clearTimeout(c)},[r.duration]);const n=()=>{switch(r.type){case"success":return e.jsx(Fe,{size:20,className:"text-adobe-success"});case"error":return e.jsx(W,{size:20,className:"text-adobe-error"});case"warning":return e.jsx(de,{size:20,className:"text-adobe-warning"});case"info":default:return e.jsx(V,{size:20,className:"text-adobe-accent"})}},i=()=>{switch(r.type){case"success":return"border-l-adobe-success";case"error":return"border-l-adobe-error";case"warning":return"border-l-adobe-warning";case"info":default:return"border-l-adobe-accent"}};return e.jsx("div",{className:`
        transform transition-all duration-200 ease-in-out
        ${t?"translate-x-full opacity-0":"translate-x-0 opacity-100"}
        bg-adobe-bg-secondary border border-adobe-border ${i()} border-l-4
        rounded-md shadow-lg p-4 mb-3 max-w-sm w-full
      `,children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"flex-shrink-0 mt-0.5",children:n()}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"text-sm font-medium text-adobe-text-primary truncate",children:r.title}),r.message&&e.jsx("p",{className:"text-xs text-adobe-text-secondary mt-1 break-words",children:r.message})]}),e.jsx("button",{onClick:o,className:"flex-shrink-0 text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(_,{size:16})})]})})},kt=()=>{const{toasts:r}=L();return r.length===0?null:e.jsx("div",{className:"fixed top-4 right-4 z-[9999] pointer-events-none",children:e.jsx("div",{className:"space-y-2 pointer-events-auto",children:r.map(a=>e.jsx(Ct,{toast:a},a.id))})})},Tt=()=>e.jsxs("div",{className:"flex flex-col h-screen bg-adobe-bg text-adobe-text font-sans",children:[e.jsx(O,{children:e.jsx(ct,{})}),e.jsx(O,{children:e.jsx(gt,{})}),e.jsx(O,{children:e.jsx(pt,{})}),e.jsx(O,{children:e.jsx(Et,{})}),e.jsx(O,{children:e.jsx(kt,{})})]});me();L.getState();D.getState().loadSettings().then(()=>{Y.createRoot(document.getElementById("root")).render(e.jsx(ie.StrictMode,{children:e.jsx(Tt,{})}))});export{Ot as D,Dt as E,_t as L,Mt as M,At as S};
