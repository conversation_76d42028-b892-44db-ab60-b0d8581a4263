const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./modelData-CihVEW1O.js","./vendor-DsceW-4w.js","./shiki-Dr4f9TeX.js","./ui-B3AhFxSy.js"])))=>i.map(i=>d[i]);
var he=Object.defineProperty;var pe=(a,o,t)=>o in a?he(a,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[o]=t;var I=(a,o,t)=>pe(a,typeof o!="symbol"?o+"":o,t);import{r as x,a as ge,R as oe}from"./vendor-DsceW-4w.js";import{_,c as fe}from"./shiki-Dr4f9TeX.js";import{c as $,L as Y,C as R,P as ye,H as ve,S as je,a as Ne,T as Se,D as we,b as Ce,M as G,A as Ee,d as ke,e as Te,f as Ie,g as ne,h as ie,R as Q,X as A,i as te,I as q,B as Le,j as Pe,k as Re,l as Ae,m as J,W as Me,n as Oe,o as _e}from"./ui-B3AhFxSy.js";(function(){const o=document.createElement("link").relList;if(o&&o.supports&&o.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))s(r);new MutationObserver(r=>{for(const n of r)if(n.type==="childList")for(const c of n.addedNodes)c.tagName==="LINK"&&c.rel==="modulepreload"&&s(c)}).observe(document,{childList:!0,subtree:!0});function t(r){const n={};return r.integrity&&(n.integrity=r.integrity),r.referrerPolicy&&(n.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?n.credentials="include":r.crossOrigin==="anonymous"?n.credentials="omit":n.credentials="same-origin",n}function s(r){if(r.ep)return;r.ep=!0;const n=t(r);fetch(r.href,n)}})();var ce={exports:{}},F={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var De=x,$e=Symbol.for("react.element"),Ue=Symbol.for("react.fragment"),Fe=Object.prototype.hasOwnProperty,ze=De.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Ke={key:!0,ref:!0,__self:!0,__source:!0};function de(a,o,t){var s,r={},n=null,c=null;t!==void 0&&(n=""+t),o.key!==void 0&&(n=""+o.key),o.ref!==void 0&&(c=o.ref);for(s in o)Fe.call(o,s)&&!Ke.hasOwnProperty(s)&&(r[s]=o[s]);if(a&&a.defaultProps)for(s in o=a.defaultProps,o)r[s]===void 0&&(r[s]=o[s]);return{$$typeof:$e,type:a,key:n,ref:c,props:r,_owner:ze.current}}F.Fragment=Ue;F.jsx=de;F.jsxs=de;ce.exports=F;var e=ce.exports,V={},se=ge;V.createRoot=se.createRoot,V.hydrateRoot=se.hydrateRoot;const He=a=>(o,t,s)=>{const r=s.subscribe;return s.subscribe=(c,d,l)=>{let u=c;if(d){const i=l?.equalityFn||Object.is;let f=c(s.getState());u=p=>{const N=c(p);if(!i(f,N)){const g=f;d(f=N,g)}},l?.fireImmediately&&d(f,f)}return r(u)},a(o,t,s)},Be=He,Ge=3e4,qe=2,z=1e4,U=1e3,Je=4e3,re=3e3,Ve=5e3,Lt=4096,Pt=8192,Rt=32e3,At=128e3,Mt=2e5,We="sahAI_settings",S="sahai-chat-history";let K=null;const P=()=>typeof window<"u"&&!!window.CSInterface,D=()=>{if(!K&&P())try{K=new window.CSInterface,console.log("CSInterface initialized successfully")}catch(a){console.error("Failed to initialize CSInterface:",a)}return K},le=()=>{if(!P()){console.warn("Not running in CEP environment");return}const a=D();if(!a)return;a.addEventListener("com.adobe.csxs.events.ThemeColorChanged",t=>{console.log("Theme changed:",t)});const o=a.getHostEnvironment();console.log("Host environment:",o),a.evalScript("SahAI.getAppInfo()",t=>{try{if(!t||t.trim()===""){console.warn("Empty response from ExtendScript");return}const s=JSON.parse(t);console.log("ExtendScript response:",s)}catch(s){console.error("Failed to parse ExtendScript response:",s,"Raw result:",t)}})},Ye=a=>a.startsWith("EvalScript error")||!a||a.trim()==="",Qe=a=>{try{return JSON.parse(a)}catch{return{success:!0,data:a}}},Xe=a=>typeof a=="object"&&a!==null&&a.success===!1,E=(a,o=Ge,t=qe)=>new Promise((s,r)=>{const n=D();if(!n){r(new Error("CSInterface not available - not running in CEP environment"));return}let c=0;const d=()=>{c++;const l=setTimeout(()=>{c<=t?(console.warn(`ExtendScript execution attempt ${c} timed out, retrying...`),d()):r(new Error(`ExtendScript execution timed out after ${o}ms (${t+1} attempts)`))},o);try{n.evalScript(a,u=>{clearTimeout(l);try{if(Ye(u)){if(c<=t){console.warn(`Retryable error on attempt ${c}, retrying...`),setTimeout(d,U);return}r(new Error(u.startsWith("EvalScript error")?`ExtendScript Error: ${u}`:"Empty response from ExtendScript after all retries"));return}const i=Qe(u);if(Xe(i)){if(c<=t){console.warn(`ExtendScript returned failure on attempt ${c}, retrying...`),setTimeout(d,U);return}r(new Error(i.message||"ExtendScript execution failed"));return}s(typeof i=="object"&&i!==null?i:{success:!0,data:i})}catch(i){if(c<=t){console.warn(`Error processing response on attempt ${c}, retrying...`),setTimeout(d,U);return}r(new Error(`Failed to process ExtendScript response: ${i}`))}})}catch(u){if(clearTimeout(l),c<=t){console.warn(`Error executing ExtendScript on attempt ${c}, retrying...`),setTimeout(d,U);return}r(new Error(`Failed to execute ExtendScript: ${u}`))}};d()});class T{static async save(o){const t=JSON.stringify(o);try{if(P())try{const s=await E(`saveSettings(${JSON.stringify(o)})`,z);if(s.success)console.log("Settings saved to CEP storage successfully");else throw new Error(s.message||"CEP save failed")}catch(s){console.warn("CEP storage save failed, falling back to localStorage:",s)}localStorage.setItem(this.SETTINGS_KEY,t),console.log("Settings saved to localStorage successfully")}catch(s){console.error("All settings save methods failed:",s);try{localStorage.setItem(this.SETTINGS_KEY,t)}catch(r){throw new Error(`Failed to save settings: ${s}. LocalStorage also failed: ${r}`)}}}static async load(){try{const o=[{name:"CEP Storage",load:async()=>{if(P()){const t=await E("loadSettings()",z);if(t.success)return t.data&&Object.keys(t.data).length>0?t.data:null}return null}},{name:"LocalStorage",load:async()=>{const t=localStorage.getItem(this.SETTINGS_KEY);if(t)try{const s=JSON.parse(t);if(s&&Object.keys(s).length>0)return s}catch(s){console.warn("Failed to parse localStorage settings:",s)}return null}}];for(const t of o)try{const s=await t.load();if(s)return console.log(`Settings loaded from ${t.name} successfully`),s}catch(s){console.warn(`Failed to load settings from ${t.name}:`,s)}return console.log("No existing settings found in any storage, returning defaults"),{providers:[]}}catch(o){return console.error("All settings load methods failed:",o),{providers:[]}}}static async exportSettings(){const o=await this.load();return JSON.stringify(o,null,2)}static async importSettings(o){try{const t=JSON.parse(o);await this.save(t)}catch{throw new Error("Invalid settings format")}}static async clearSettings(){try{if(P())try{await E("saveSettings({})",z)}catch(o){console.warn("Failed to clear CEP storage:",o)}localStorage.removeItem(this.SETTINGS_KEY),console.log("Settings cleared successfully")}catch(o){throw new Error(`Failed to clear settings: ${o}`)}}}I(T,"SETTINGS_KEY",We);class X{static async checkProviderStatus(o,t){const s=Date.now();try{const{ProviderBridge:r}=await _(async()=>{const{ProviderBridge:c}=await Promise.resolve().then(()=>ue);return{ProviderBridge:c}},void 0,import.meta.url);return{isOnline:(await r.listModels(o,t.baseURL,t.apiKey)).length>0,latency:Date.now()-s}}catch(r){return{isOnline:!1,error:r.message||String(r),latency:Date.now()-s}}}}Promise.prototype.timeout||(Promise.prototype.timeout=function(a){return Promise.race([this,new Promise((o,t)=>setTimeout(()=>t(new Error(`Operation timed out after ${a}ms`)),a))])});const Ze={async listModels(a,o,t){return new Promise((s,r)=>{const n=D();if(!n){r(new Error("CSInterface not available - not running in CEP environment"));return}const c=`listModels("${a}", "${o||""}", "${t||""}")`;n.evalScript(c,d=>{try{if(!d||d.trim()===""||d.trim()==="undefined")return r(new Error("ExtendScript returned an empty response. Check if the script is properly loaded."));if(d.startsWith("EvalScript error"))return r(new Error(`ExtendScript execution failed: ${d}`));const l=JSON.parse(d);if(l.success){const u=l.data.map(i=>({id:i.id||"unknown-id",name:i.name||i.id||"Unknown Model",description:i.description||"",contextLength:i.contextLength||i.context_length||4096,isRecommended:i.isRecommended||i.is_recommended||!1}));s(u)}else throw new Error(l.message||"An unknown ExtendScript error occurred.")}catch(l){r(new Error(`Failed to parse response from host: ${l.message}. Response: ${d}`))}})})},async pullModel(a,o,t){if(a!=="ollama")throw new Error("Pull only supported for Ollama");return new Promise((s,r)=>{const n=D();if(!n){r(new Error("CSInterface not available - not running in CEP environment"));return}const c=t.replace("http://","").split(":")[0],d=parseInt(t.split(":")[2]||"11434",10),l=`
        try {
          var result = makeRequest('${c}', '/api/pull', 'POST', null, ${d}, JSON.stringify({name: '${o}'}));
          JSON.stringify({success: true, message: 'Model pull initiated'});
        } catch (e) {
          JSON.stringify({success: false, message: e.toString()});
        }
      `;n.evalScript(l,u=>{try{const i=JSON.parse(u);i.success?s(i):r(new Error(i.message))}catch(i){r(new Error(`Failed to parse pull response: ${i}`))}})})},async getFallbackModels(a){const{getFallbackModels:o}=await _(async()=>{const{getFallbackModels:t}=await import("./modelData-CihVEW1O.js");return{getFallbackModels:t}},__vite__mapDeps([0,1,2,3]),import.meta.url);return o(a)}},ue=Object.freeze(Object.defineProperty({__proto__:null,CEPSettings:T,ProviderBridge:Ze,ProviderStatusChecker:X,executeExtendScript:E,getCSInterface:D,initializeCEP:le,isCEPEnvironment:P},Symbol.toStringTag,{value:"Module"})),k=$((a,o)=>({toasts:[],addToast:t=>{const s=`toast-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,r={...t,id:s,isVisible:!0,duration:t.duration||Je};a(n=>({toasts:[...n.toasts,r]})),setTimeout(()=>{o().removeToast(s)},r.duration)},removeToast:t=>{a(s=>({toasts:s.toasts.filter(r=>r.id!==t)}))},clearAllToasts:()=>{a({toasts:[]})}})),H={success:(a,o,t)=>{k.getState().addToast({type:"success",title:a,message:o,duration:t})},error:(a,o,t)=>{k.getState().addToast({type:"error",title:a,message:o,duration:t})},warning:(a,o,t)=>{k.getState().addToast({type:"warning",title:a,message:o,duration:t})},info:(a,o,t)=>{k.getState().addToast({type:"info",title:a,message:o,duration:t})}},et=[{pattern:/timeout|timed out/i,code:"TIMEOUT_ERROR",userMessage:"Request timed out. Please check your internet connection and try again.",severity:"medium",retryable:!0},{pattern:/network|connection|ECONNREFUSED|ENOTFOUND/i,code:"NETWORK_ERROR",userMessage:"Network error. Please check your internet connection.",severity:"medium",retryable:!0},{pattern:/failed to connect|connection failed/i,code:"CONNECTION_FAILED",userMessage:"Failed to connect to the service. Please try again later.",severity:"medium",retryable:!0},{pattern:/unauthorized|401/i,code:"UNAUTHORIZED",userMessage:"Invalid API key. Please check your credentials.",severity:"high",retryable:!1},{pattern:/forbidden|403/i,code:"FORBIDDEN",userMessage:"Access denied. Please check your API key permissions.",severity:"high",retryable:!1},{pattern:/invalid.*api.*key|api.*key.*invalid/i,code:"INVALID_API_KEY",userMessage:"Invalid API key format. Please check your API key.",severity:"high",retryable:!1},{pattern:/not found|404/i,code:"NOT_FOUND",userMessage:"API endpoint not found. Please check the provider configuration.",severity:"medium",retryable:!1},{pattern:/rate limit|429|too many requests/i,code:"RATE_LIMITED",userMessage:"Rate limit exceeded. Please wait a moment and try again.",severity:"medium",retryable:!0},{pattern:/server error|500|502|503|504/i,code:"SERVER_ERROR",userMessage:"Server error. Please try again later.",severity:"medium",retryable:!0},{pattern:/bad request|400/i,code:"BAD_REQUEST",userMessage:"Invalid request. Please check your configuration.",severity:"medium",retryable:!1},{pattern:/CSInterface not available|not running in CEP/i,code:"CEP_NOT_AVAILABLE",userMessage:"CEP environment not available. Please run this in Adobe application.",severity:"high",retryable:!1},{pattern:/ExtendScript|EvalScript error/i,code:"EXTENDSCRIPT_ERROR",userMessage:"Script execution error. Please try again.",severity:"medium",retryable:!0},{pattern:/API Key required|Base URL required/i,code:"MISSING_CONFIG",userMessage:"Configuration missing. Please provide required credentials.",severity:"medium",retryable:!1},{pattern:/invalid.*config|config.*invalid/i,code:"INVALID_CONFIG",userMessage:"Invalid configuration. Please check your settings.",severity:"medium",retryable:!1}];function me(a){const o=a?.message||String(a);for(const t of et)if(t.pattern instanceof RegExp?t.pattern.test(o):o.includes(t.pattern))return{code:t.code,message:o,userMessage:t.userMessage,severity:t.severity,retryable:t.retryable};return{code:"UNKNOWN_ERROR",message:o,userMessage:"An unexpected error occurred. Please try again.",severity:"medium",retryable:!0}}function tt(a,o){const t=me(a);return`${o?`[${o}] `:""}${t.code}: ${t.message}`}const st=a=>a.configType==="baseURL"&&!a.baseURL?"Base URL required":a.configType==="apiKey"&&!a.apiKey?"API Key required":null,rt=(a,o)=>a.map(t=>({id:t.id||"unknown-id",name:t.name||t.id||"Unknown Model",description:t.description||"",contextLength:t.contextLength||4096,isRecommended:t.isRecommended||!1})),M=$()(Be((a,o)=>({providers:[{id:"openai",name:"OpenAI",configType:"apiKey",isConfigured:!1,models:[]},{id:"anthropic",name:"Anthropic",configType:"apiKey",isConfigured:!1,models:[]},{id:"gemini",name:"Google Gemini",configType:"apiKey",isConfigured:!1,models:[]},{id:"groq",name:"Groq",configType:"apiKey",isConfigured:!1,models:[]},{id:"deepseek",name:"DeepSeek",configType:"apiKey",isConfigured:!1,models:[]},{id:"openrouter",name:"OpenRouter",configType:"apiKey",isConfigured:!1,models:[]},{id:"ollama",name:"Ollama",configType:"baseURL",isConfigured:!1,models:[]}],activeProviderId:"openai",isLoadingModels:!1,setActiveProvider:t=>{a(r=>({activeProviderId:t,providers:r.providers.map(n=>n.id!==t?{...n,models:[],isLoading:!1,error:void 0}:n)})),o().persistSettings();const s=o().providers.find(r=>r.id===t);s&&s.isConfigured&&(s.apiKey||s.baseURL)&&t!=="ollama"&&o().loadModelsForProvider(t)},updateProviderConfig:(t,s)=>{a(r=>({providers:r.providers.map(n=>n.id===t?{...n,...s,isConfigured:!!(s.apiKey||s.baseURL)}:n)})),o().persistSettings(),(s.apiKey||s.baseURL)&&t!=="ollama"&&o().loadModelsForProvider(t)},setProviderModels:(t,s)=>{a(r=>({providers:r.providers.map(n=>n.id===t?{...n,models:s,isLoading:!1,error:void 0}:n)}))},setSelectedModel:(t,s)=>{a(r=>({providers:r.providers.map(n=>n.id===t?{...n,selectedModelId:s}:n)})),o().persistSettings()},updateProviderKey:(t,s,r)=>{a(n=>({providers:n.providers.map(c=>c.id===t?{...c,apiKey:s,isConfigured:!!s,selectedModelId:r||c.selectedModelId}:c)})),o().persistSettings(),s&&t!=="ollama"&&o().loadModelsForProvider(t)},saveProviderSelection:(t,s)=>{const r=o().providers.find(n=>n.id===t);a(n=>({activeProviderId:t,providers:n.providers.map(c=>c.id===t?{...c,...s,isConfigured:!!(s.apiKey||s.baseURL)}:c)})),o().persistSettings(),H.success("Provider configured",`${r?.name||t} has been configured successfully`,re),(s.apiKey||s.baseURL)&&t!=="ollama"&&o().loadModelsForProvider(t)},loadModelsForProvider:async t=>{const s=o().providers.find(n=>n.id===t);if(!s)return;if(s.isLoading){console.log(`Already loading models for ${t}, skipping...`);return}const r=st(s);if(r){a(n=>({providers:n.providers.map(c=>c.id===t?{...c,error:r}:c)}));return}console.log(`Loading models for provider: ${t}`),a(n=>({providers:n.providers.map(c=>c.id===t?{...c,isLoading:!0,error:void 0}:c)}));try{const{ProviderBridge:n}=await _(async()=>{const{ProviderBridge:u}=await Promise.resolve().then(()=>ue);return{ProviderBridge:u}},void 0,import.meta.url),c=new Promise((u,i)=>{setTimeout(()=>i(new Error("Request timed out after 30 seconds")),3e4)}),d=await Promise.race([n.listModels(t,s.baseURL,s.apiKey),c]);console.log(`Received ${d.length} models for ${t}:`,d);const l=rt(d,t);console.log(`Transformed models for ${t}:`,l),o().setProviderModels(t,l),H.success("Models loaded successfully",`Found ${l.length} models for ${s.name}`,re)}catch(n){const c=me(n);console.error(tt(n,`loadModelsForProvider:${t}`)),a(d=>({providers:d.providers.map(l=>l.id===t?{...l,isLoading:!1,error:c.userMessage}:l)})),H.error("Failed to load models",`${s.name}: ${c.userMessage}`,Ve)}},persistSettings:()=>{const{activeProviderId:t,providers:s}=o();T.save({activeProviderId:t,providers:s.map(r=>({id:r.id,isConfigured:r.isConfigured,configType:r.configType,apiKey:r.apiKey,baseURL:r.baseURL,selectedModelId:r.selectedModelId,settings:r.settings}))})},loadSettings:async()=>{try{const t=await T.load();t.activeProviderId?a({activeProviderId:t.activeProviderId}):a({activeProviderId:"openai"}),t.providers&&Array.isArray(t.providers)&&a(n=>({providers:n.providers.map(c=>{const d=t.providers?.find(l=>l.id===c.id);return d?{...c,...d}:c})}));const s=o(),r=s.providers.find(n=>n.id===s.activeProviderId);r&&r.isConfigured&&(r.apiKey||r.baseURL)&&r.id!=="ollama"&&o().loadModelsForProvider(r.id)}catch(t){console.error("Failed to load CEP settings:",t)}},getActiveProvider:()=>{const{providers:t,activeProviderId:s}=o();return t.find(r=>r.id===s)||null},getActiveModel:()=>{const t=o().getActiveProvider();return t?.selectedModelId&&t.models.find(s=>s.id===t.selectedModelId)||null}}))),O=$(a=>({modal:null,openModal:o=>a({modal:o}),closeModal:()=>a({modal:null})})),Z=$((a,o)=>({messages:[],isLoading:!1,addMessage:t=>{const s={...t,id:crypto.randomUUID(),timestamp:Date.now()};a(n=>({messages:[...n.messages,s]}));const r=o().currentSession;if(r){const n=()=>{_(async()=>{const{useHistoryStore:c}=await Promise.resolve().then(()=>ae);return{useHistoryStore:c}},void 0,import.meta.url).then(({useHistoryStore:c})=>{const d=c.getState(),l=d.sessions.find(u=>u.id===r);if(l){const u=o().messages,i={...l,messages:u,updatedAt:Date.now(),title:l.title===`Chat ${new Date(l.createdAt).toLocaleDateString()}`&&u[0]?.content.slice(0,50)+"..."||l.title};d.saveSession(i)}}).catch(c=>{console.error("Failed to import history store:",c)})};window.requestIdleCallback?window.requestIdleCallback(n):setTimeout(n,0)}},setLoading:t=>a({isLoading:t}),createNewSession:()=>{const t=crypto.randomUUID();return a({messages:[],currentSession:t}),_(async()=>{const{useHistoryStore:s}=await Promise.resolve().then(()=>ae);return{useHistoryStore:s}},void 0,import.meta.url).then(({useHistoryStore:s})=>{s.getState().createSession()}).catch(s=>{console.error("Failed to import history store:",s)}),t},loadSession:(t,s)=>{a({currentSession:t,messages:s})},clearMessages:()=>{a({messages:[],currentSession:void 0})}})),at=()=>{const{getActiveProvider:a}=M(),[o,t]=x.useState({isOnline:null,isChecking:!1}),s=a();x.useEffect(()=>{let n;const c=async()=>{if(!s?.isConfigured){t({isOnline:null,isChecking:!1});return}t(d=>({...d,isChecking:!0,error:void 0}));try{const d=await X.checkProviderStatus(s.id,{apiKey:s.apiKey,baseURL:s.baseURL});t({isOnline:d.isOnline,latency:d.latency,isChecking:!1})}catch(d){t({isOnline:!1,isChecking:!1,error:d.message})}};return s?.isConfigured&&(c(),n=setInterval(c,3e4)),()=>{n&&clearInterval(n)}},[s]);const r=()=>{const n="w-2.5 h-2.5 rounded-full transition-all duration-300 shadow-sm hover:scale-110 cursor-pointer";return o.isChecking?`${n} bg-adobe-warning animate-pulse shadow-adobe-warning/40`:o.isOnline===!0?`${n} bg-adobe-success shadow-adobe-success/40`:`${n} bg-adobe-error shadow-adobe-error/40`};return e.jsx("div",{className:r()})},ot=()=>{const{getActiveProvider:a,getActiveModel:o,loadSettings:t}=M(),{openModal:s}=O(),{createNewSession:r}=Z(),[,n]=x.useState({}),c=a(),d=o(),l=c?.isLoading||!1,u=x.useMemo(()=>c?l?`${c.name} • Loading models...`:d?`${c.name} • ${d.name}`:`${c.name} • Select Model`:"Select AI Provider & Model",[c,d,l]);return x.useEffect(()=>{t()},[t]),x.useEffect(()=>M.subscribe(()=>n({})),[]),e.jsxs("header",{className:"flex items-center justify-between px-4 py-3 border-b border-adobe-border bg-adobe-bg-secondary shadow-sm",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("button",{onClick:()=>s("status"),className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all flex items-center justify-center",children:e.jsx(at,{})}),e.jsxs("button",{onClick:()=>s("provider"),className:"flex items-center space-x-2 text-sm font-medium text-adobe-text-primary hover:text-adobe-accent transition-colors group",title:"Select AI Provider & Model",children:[e.jsx("span",{className:"max-w-[300px] truncate",children:u}),l?e.jsx(Y,{size:14,className:"animate-spin text-adobe-text-secondary"}):e.jsx(R,{size:14,className:"text-adobe-text-secondary group-hover:text-adobe-accent transition-colors"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{onClick:r,className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all",title:"New Chat",children:e.jsx(ye,{size:16})}),e.jsx("div",{className:"h-4 w-px bg-adobe-border"}),e.jsx("button",{onClick:()=>s("chat-history"),className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all",title:"Chat History",children:e.jsx(ve,{size:16})}),e.jsx("button",{onClick:()=>s("settings"),className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all",title:"Settings",children:e.jsx(je,{size:16})})]})]})},nt=["javascript","typescript","jsx","tsx","json","xml","markdown","shell"],it=["github-dark"],ct=fe({themes:it,langs:nt});let B=null;async function dt(){return B||(B=await ct),B}function xe(a){return["javascript","typescript","jsx","tsx","json","xml","markdown","shell"].includes(a.toLowerCase())}function lt(a){const o={js:"javascript",ts:"typescript",bash:"shell",sh:"shell",zsh:"shell",fish:"shell",py:"javascript",rb:"javascript",yml:"json",yaml:"json",htm:"json",html:"json",css:"json",sass:"json",scss:"json"},t=a.toLowerCase();return o[t]?o[t]:xe(t)?t:"javascript"}const ut=({content:a})=>{const[o,t]=x.useState(null),[s,r]=x.useState({});x.useEffect(()=>{dt().then(t)},[]);const n=l=>{r(u=>({...u,[l]:{...u[l],isCollapsed:!u[l]?.isCollapsed}}))},c=async(l,u)=>{try{if(u==="javascript"||u==="jsx"){const i=await E(`
          try {
            ${l}
          } catch (error) {
            return "Error: " + error.toString();
          }
        `);console.log("Code execution result:",i)}else await navigator.clipboard.writeText(l),console.log("Code copied to clipboard for manual execution")}catch(i){console.error("Failed to execute code:",i)}};if(!o)return e.jsx("pre",{className:"whitespace-pre-wrap",children:a});const d=a.split(/(```[\s\S]*?```)/g);return e.jsx(e.Fragment,{children:d.map((l,u)=>{if(l.startsWith("```")){const i=l.split(`
`),f=i[0].replace("```","").trim(),p=i.slice(1,-1).join(`
`),N=xe(f)?f:lt(f),g=`code-block-${u}`,h=s[g]?.isCollapsed||!1,m=["javascript","jsx","typescript","tsx"].includes(N);return e.jsxs("div",{className:"relative border border-adobe-border rounded-lg overflow-hidden bg-adobe-bg-secondary",children:[e.jsxs("div",{className:"flex items-center justify-between px-3 py-2 bg-adobe-bg-tertiary border-b border-adobe-border",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-xs font-mono text-adobe-text-secondary uppercase",children:f||"code"}),e.jsxs("span",{className:"text-xs text-adobe-text-secondary",children:[p.split(`
`).length," lines"]})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("button",{title:"Copy to clipboard",onClick:()=>navigator.clipboard.writeText(p),className:"p-1.5 hover:bg-adobe-bg-primary/80 rounded text-xs transition-colors",children:e.jsx(Ne,{size:14})}),m&&e.jsx("button",{title:"Run in terminal",onClick:()=>c(p,N),className:"p-1.5 hover:bg-adobe-bg-primary/80 rounded text-xs transition-colors text-adobe-accent",children:e.jsx(Se,{size:14})}),e.jsx("button",{title:"Save to file",className:"p-1.5 hover:bg-adobe-bg-primary/80 rounded text-xs transition-colors",children:e.jsx(we,{size:14})}),e.jsx("button",{title:h?"Expand":"Collapse",onClick:()=>n(g),className:"p-1.5 hover:bg-adobe-bg-primary/80 rounded text-xs transition-colors",children:h?e.jsx(R,{size:14}):e.jsx(Ce,{size:14})})]})]}),!h&&e.jsx("div",{className:"relative",children:e.jsx("div",{className:"overflow-x-auto",dangerouslySetInnerHTML:{__html:o.codeToHtml(p,{lang:N,theme:"github-dark"})}})}),h&&e.jsx("div",{className:"px-3 py-2 text-adobe-text-secondary text-sm italic",children:"Code block collapsed. Click expand to view."})]},u)}return e.jsx("div",{children:l},u)})})},mt=({message:a})=>{const o=a.role==="user";return e.jsx("div",{className:`flex gap-3 ${o?"justify-end":"justify-start"} mb-4`,children:e.jsx("div",{className:`max-w-[85%] rounded-2xl px-4 py-3 text-sm leading-relaxed shadow-sm ${o?"bg-adobe-bg-tertiary text-adobe-text-primary ml-8 rounded-br-md":"bg-adobe-bg-primary text-adobe-text-primary mr-8 rounded-bl-md border border-adobe-border"}`,children:e.jsx("div",{className:"whitespace-pre-wrap",children:e.jsx(ut,{content:a.content})})})})};function W(a,o){let t=null,s=0;return(...r)=>{const n=Date.now();n-s>o?(s=n,a(...r)):(t&&clearTimeout(t),t=setTimeout(()=>{s=Date.now(),a(...r),t=null},o-(n-s)))}}const xt=()=>{const{messages:a,isLoading:o,currentSession:t}=Z(),s=x.useRef(null),r=x.useRef(null),[n,c]=x.useState(!1),d=x.useRef();x.useEffect(()=>{const i=r.current,f=s.current;if(!i||!f)return;const{scrollTop:p,scrollHeight:N,clientHeight:g}=i;N-(p+g)<150&&requestAnimationFrame(()=>{f.scrollIntoView({behavior:"smooth"})})},[a.length,o]);const l=x.useCallback(W(()=>{const i=r.current;if(!i)return;clearTimeout(d.current);const{scrollTop:f,scrollHeight:p,clientHeight:N}=i,g=p-(f+N)<100;c(!g),d.current=setTimeout(()=>{c(!1)},2e3)},100),[]);x.useEffect(()=>{const i=r.current;if(i)return i.addEventListener("scroll",l,{passive:!0}),()=>{i.removeEventListener("scroll",l),clearTimeout(d.current)}},[l]);const u=()=>{s.current?.scrollIntoView({behavior:"smooth"})};return e.jsxs("div",{ref:r,className:`flex-1 overflow-y-auto px-3 py-2 space-y-4
                chat-messages-scrollbar
                relative`,children:[(!t||a.length===0)&&e.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-adobe-text-secondary gap-3",children:[e.jsx("div",{className:"w-20 h-20 mb-2 flex items-center justify-center",children:e.jsx(G,{size:80,className:"text-adobe-text-secondary"})}),e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary",children:"Start a conversation"}),e.jsx("p",{className:"text-sm text-center max-w-md",children:"Type a message below to begin chatting with SahAI"})]}),a.map(i=>e.jsx(mt,{message:i},i.id)),o&&e.jsx("div",{className:"flex items-center gap-2 text-adobe-text-secondary text-sm",children:e.jsx("span",{children:"AI is thinking..."})}),e.jsx("div",{ref:s}),n&&e.jsx("button",{onClick:u,className:"absolute right-4 bottom-4 p-2 rounded-full bg-adobe-bg-tertiary border border-adobe-border text-adobe-text-primary hover:bg-adobe-bg-secondary transition-all duration-300 shadow-md","aria-label":"Scroll to bottom",children:e.jsx(Ee,{size:18})})]})},be=x.forwardRef(({value:a,onChange:o,minHeight:t=72,maxHeight:s=200,onHeightChange:r,className:n="",style:c,...d},l)=>{const u=x.useRef(null),i=x.useRef(t),f=x.useCallback(g=>{u.current=g,typeof l=="function"?l(g):l&&(l.current=g)},[l]),p=x.useCallback(()=>{const g=u.current;if(!g)return t;g.style.height="auto";const h=g.scrollHeight,m=Math.min(Math.max(h,t),s);return g.style.height=`${m}px`,m},[t,s]),N=x.useCallback(g=>{o(g),requestAnimationFrame(()=>{const h=p();h!==i.current&&(i.current=h,r?.(h))})},[o,p,r]);return x.useEffect(()=>{!a&&u.current&&(u.current.style.height=`${t}px`,i.current!==t&&(i.current=t,r?.(t)))},[a,t,r]),x.useEffect(()=>{if(u.current){const g=p();i.current=g,r?.(g)}},[p,r]),e.jsx("textarea",{ref:f,value:a,onChange:N,className:`resize-none transition-all duration-150 ease-out ${n}`,style:{minHeight:`${t}px`,maxHeight:`${s}px`,height:`${t}px`,...c},...d})});be.displayName="AutoResizeTextarea";const bt=oe.memo(()=>{const[a,o]=x.useState(""),[t,s]=x.useState(!1),r=x.useRef(null),{addMessage:n,isLoading:c,setLoading:d,currentSession:l,createNewSession:u}=Z(),i=4e3,f=!a.trim(),p=a.length>i*.9,N=x.useCallback(y=>{o(y.target.value)},[]),g=x.useCallback(async()=>{const y=a.trim();if(!(!y||c)){o("");try{d(!0),l||u(),n({content:y,role:"user"}),setTimeout(()=>{n({content:`Echo: ${y}`,role:"assistant"}),d(!1)},1e3)}catch{o(y),d(!1)}}},[a,c,l,n,d,u]),h=x.useCallback(y=>{y.key==="Enter"&&!y.shiftKey&&!t&&(y.preventDefault(),g())},[g,t]),m=x.useCallback(()=>{console.log("File attachment clicked - functionality to be implemented")},[]),b=x.useCallback(()=>{console.log("Voice input clicked - functionality to be implemented")},[]);return e.jsxs("div",{className:"px-4 pb-3 pt-2 bg-adobe-bg-secondary border-t border-adobe-border",children:[e.jsxs("div",{className:"relative flex items-center bg-transparent rounded-lg border border-adobe-text-secondary/50 focus-within:border-adobe-accent focus-within:ring-1 focus-within:ring-adobe-accent transition-colors",children:[e.jsx("div",{className:"flex items-center pl-3",children:e.jsx("button",{onClick:m,className:"text-adobe-text-secondary hover:text-adobe-accent transition p-1.5 rounded",title:"Attach file",disabled:c,children:e.jsx(ke,{size:18})})}),e.jsx(be,{ref:r,maxLength:i,value:a,onChange:N,onKeyDown:h,onCompositionStart:()=>s(!0),onCompositionEnd:()=>s(!1),placeholder:"Type a message...",disabled:c,minHeight:72,maxHeight:200,className:"flex-1 bg-transparent text-adobe-text-primary text-sm p-3 outline-none placeholder:text-adobe-text-secondary/80 leading-relaxed overflow-y-auto chat-messages-scrollbar"}),e.jsxs("div",{className:"flex items-center pr-3 space-x-1",children:[e.jsx("button",{onClick:b,className:"text-adobe-text-secondary hover:text-adobe-warning transition p-1.5 rounded disabled:opacity-40",title:"Voice input",disabled:c,children:e.jsx(Te,{size:18})}),e.jsx("button",{onClick:g,disabled:f||c,className:"text-adobe-accent hover:text-adobe-accent-hover transition p-1.5 rounded disabled:text-adobe-text-secondary/50 disabled:hover:text-adobe-text-secondary/50",title:"Send",children:c?e.jsx(Y,{size:18,className:"animate-spin"}):e.jsx(Ie,{size:18})})]})]}),e.jsxs("div",{className:"flex justify-between items-center mt-1 px-1",children:[e.jsxs("span",{className:`text-xs ${p?"text-adobe-warning":"text-adobe-text-secondary"}`,children:[a.length,"/",i]}),e.jsx("span",{className:"text-xs text-adobe-text-secondary",children:"Enter to send, Shift+Enter for new line"})]})]})}),ht=({models:a,value:o,onChange:t,placeholder:s="Search models..."})=>{const[r,n]=x.useState(""),[c,d]=x.useState(!1),[l,u]=x.useState(0),i=x.useRef(null),f=x.useRef(null),p=a.filter(m=>m.name.toLowerCase().includes(r.toLowerCase())||m.id.toLowerCase().includes(r.toLowerCase())),N=a.find(m=>m.id===o),g=x.useCallback(m=>{t(m),d(!1),n(""),u(0)},[t]),h=x.useCallback(m=>{if(!c&&(m.key==="ArrowDown"||m.key==="ArrowUp")){m.preventDefault(),d(!0),n("");return}if(c)switch(m.key){case"ArrowDown":m.preventDefault(),u(b=>b<p.length-1?b+1:b);break;case"ArrowUp":m.preventDefault(),u(b=>b>0?b-1:0);break;case"Enter":m.preventDefault(),p[l]&&g(p[l].id);break;case"Escape":m.preventDefault(),d(!1),u(0);break}},[c,p,l,g]);return x.useEffect(()=>{const m=b=>{i.current&&!i.current.contains(b.target)&&(d(!1),u(0))};return document.addEventListener("mousedown",m),()=>document.removeEventListener("mousedown",m)},[]),x.useEffect(()=>{u(0)},[p]),e.jsxs("div",{className:"relative",ref:i,children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{ref:f,type:"text",value:c?r:N?.name||"",onChange:m=>{n(m.target.value),d(!0),u(0)},onFocus:()=>{d(!0),n(""),u(0)},onKeyDown:h,placeholder:s,className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-3 py-2 text-adobe-text-primary focus-within:border-adobe-accent outline-none pr-10",readOnly:!c}),e.jsx("button",{onClick:()=>{c&&p.length>0?g(p[l]?.id||p[0].id):(d(!0),n(""),u(0))},className:"absolute right-2 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary hover:text-adobe-text-primary",children:e.jsx(ne,{size:18})})]}),c&&e.jsx("div",{className:"absolute z-[9999] mt-1 w-full bg-adobe-bg-secondary border border-adobe-border rounded-md shadow-lg max-h-52 overflow-auto",children:p.length>0?p.map((m,b)=>e.jsx("div",{className:`px-4 py-2 cursor-pointer ${o===m.id?"bg-adobe-accent text-white":b===l?"bg-adobe-bg-tertiary text-adobe-text-primary":"hover:bg-adobe-bg-tertiary text-adobe-text-primary"}`,onClick:()=>g(m.id),onMouseEnter:()=>u(b),children:m.name},m.id)):e.jsx("div",{className:"px-4 py-2 text-adobe-text-secondary",children:"No models found"})})]})},pt=({providers:a,value:o,onChange:t,placeholder:s="Search providers..."})=>{const[r,n]=x.useState(""),[c,d]=x.useState(!1),[l,u]=x.useState(0),i=x.useRef(null),f=x.useRef(null),p=a.filter(m=>m.name.toLowerCase().includes(r.toLowerCase())||m.id.toLowerCase().includes(r.toLowerCase())),N=a.find(m=>m.id===o),g=x.useCallback(m=>{t(m),d(!1),n(""),u(0)},[t]),h=x.useCallback(m=>{if(!c&&(m.key==="ArrowDown"||m.key==="ArrowUp")){m.preventDefault(),d(!0),n("");return}if(c)switch(m.key){case"ArrowDown":m.preventDefault(),u(b=>b<p.length-1?b+1:b);break;case"ArrowUp":m.preventDefault(),u(b=>b>0?b-1:0);break;case"Enter":m.preventDefault(),p[l]&&g(p[l].id);break;case"Escape":m.preventDefault(),d(!1),u(0);break}},[c,p,l,g]);return x.useEffect(()=>{const m=b=>{i.current&&!i.current.contains(b.target)&&(d(!1),u(0))};return document.addEventListener("mousedown",m),()=>document.removeEventListener("mousedown",m)},[]),x.useEffect(()=>{u(0)},[p]),e.jsxs("div",{className:"relative",ref:i,children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{ref:f,type:"text",value:c?r:N?.name||"",onChange:m=>{n(m.target.value),d(!0),u(0)},onFocus:()=>{d(!0),n(""),u(0)},onKeyDown:h,placeholder:s,className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-lg px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-2 focus:ring-adobe-accent/20 outline-none transition-all pr-10",readOnly:!c}),e.jsx("button",{onClick:()=>{c&&p.length>0?g(p[l]?.id||p[0].id):(d(!0),n(""),u(0))},className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(R,{size:18,className:`transition-transform ${c?"rotate-180":""}`})})]}),c&&e.jsx("div",{className:"absolute z-[9999] mt-1 w-full bg-adobe-bg-secondary border border-adobe-border rounded-lg shadow-lg max-h-52 overflow-auto",children:p.length>0?p.map((m,b)=>e.jsxs("div",{className:`px-4 py-3 cursor-pointer flex items-center justify-between ${o===m.id?"bg-adobe-accent text-white":b===l?"bg-adobe-bg-tertiary text-adobe-text-primary":"hover:bg-adobe-bg-tertiary text-adobe-text-primary"}`,onClick:()=>g(m.id),onMouseEnter:()=>u(b),children:[e.jsx("span",{children:m.name}),m.isConfigured&&e.jsx("div",{className:`w-2 h-2 rounded-full ${o===m.id?"bg-white":"bg-green-500"}`})]},m.id)):e.jsx("div",{className:"px-4 py-3 text-adobe-text-secondary",children:"No providers found"})})]})};class L extends x.Component{constructor(){super(...arguments);I(this,"state",{hasError:!1})}static getDerivedStateFromError(t){return{hasError:!0,error:t}}componentDidCatch(t,s){console.error("Uncaught error:",t,s)}render(){return this.state.hasError?this.props.fallback?this.props.fallback:e.jsxs("div",{className:"flex flex-col items-center justify-center p-8 bg-adobe-bg-primary text-adobe-text-primary",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Something went wrong"}),e.jsx("p",{className:"text-adobe-text-secondary mb-4",children:this.state.error?.message||"An unexpected error occurred"}),e.jsx("button",{className:"px-4 py-2 bg-adobe-accent text-white rounded-md hover:bg-adobe-accent/90",onClick:()=>this.setState({hasError:!1,error:void 0}),children:"Try again"})]}):this.props.children}}class gt extends x.Component{constructor(){super(...arguments);I(this,"retryTimeout");I(this,"state",{hasError:!1,errorCount:0});I(this,"handleRetry",()=>{this.setState({hasError:!1,error:void 0}),this.props.onRetry&&this.props.onRetry()})}static getDerivedStateFromError(t){return{hasError:!0,error:t}}componentDidCatch(t,s){if(console.error(`Provider ${this.props.providerId} error:`,t,s),this.setState(r=>({errorCount:r.errorCount+1})),this.state.errorCount>=3){console.warn(`Provider ${this.props.providerId} has failed ${this.state.errorCount} times, stopping auto-retry`);return}this.state.errorCount<2&&this.props.providerId!=="ollama"&&(this.retryTimeout=setTimeout(()=>{this.handleRetry()},2e3*this.state.errorCount))}componentWillUnmount(){this.retryTimeout&&window.clearTimeout(this.retryTimeout)}render(){if(this.state.hasError){const t=this.state.errorCount>=3;return e.jsxs("div",{className:"flex flex-col items-center justify-center p-6 bg-adobe-bg-secondary border border-adobe-border rounded-lg",children:[e.jsx(ie,{size:32,className:"text-adobe-warning mb-3"}),e.jsx("h3",{className:"text-lg font-semibold text-adobe-text-primary mb-2",children:"Provider Error"}),e.jsx("p",{className:"text-adobe-text-secondary text-sm text-center mb-4",children:t?`${this.props.providerId} has encountered multiple errors. Please check your configuration.`:`There was an error loading ${this.props.providerId}. ${this.state.errorCount<2?"Retrying automatically...":""}`}),this.state.error&&e.jsxs("details",{className:"mb-4 w-full",children:[e.jsx("summary",{className:"text-xs text-adobe-text-secondary cursor-pointer hover:text-adobe-text-primary",children:"Error Details"}),e.jsx("pre",{className:"text-xs text-adobe-error mt-2 p-2 bg-adobe-bg-primary rounded border overflow-auto max-h-20",children:this.state.error.message})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("button",{onClick:this.handleRetry,className:"flex items-center gap-2 px-3 py-1.5 bg-adobe-accent text-white rounded text-sm hover:bg-adobe-accent/90 transition-colors",children:[e.jsx(Q,{size:14}),"Try Again"]}),t&&e.jsx("button",{onClick:()=>this.setState({hasError:!1,error:void 0,errorCount:0}),className:"px-3 py-1.5 border border-adobe-border text-adobe-text-primary rounded text-sm hover:bg-adobe-bg-tertiary transition-colors",children:"Reset"})]}),e.jsxs("p",{className:"text-xs text-adobe-text-secondary mt-3 text-center",children:["Error count: ",this.state.errorCount,"/3"]})]})}return this.props.children}}const ft=()=>{const{closeModal:a}=O(),{providers:o,activeProviderId:t,saveProviderSelection:s,loadModelsForProvider:r,updateProviderConfig:n}=M(),[c,d]=x.useState(t||""),l=x.useCallback(W(h=>{r(h)},1e3),[r]),u=x.useCallback(W((h,m)=>{n(h,m)},500),[n]),i=o.find(h=>h.id===c);x.useEffect(()=>{if(i&&!i.selectedModelId){if(i.models.length>0){const h=i.models[0];n(i.id,{selectedModelId:h.id})}}},[i,n]),x.useEffect(()=>{i&&i.isConfigured&&!i.models.length&&!i.isLoading&&i.id!=="ollama"&&l(i.id)},[i,l]);const f=x.useCallback(h=>{if(!i)return;const m=i.configType==="apiKey"?{apiKey:h}:{baseURL:h};u(i.id,m),h.trim()&&i.id!=="ollama"&&l(i.id)},[i,u,l]),p=()=>{if(!c||!i)return;const h={apiKey:i.configType==="apiKey"?i.apiKey:void 0,baseURL:i.configType==="baseURL"?i.baseURL:void 0,selectedModelId:i.selectedModelId};!(i.configType==="apiKey"?h.apiKey:h.baseURL)||!h.selectedModelId||(s(c,h),a())},N=i?.models.map(h=>({id:h.id,name:h.name}))||[],g=o.map(h=>({id:h.id,name:h.name,isConfigured:h.isConfigured}));return e.jsx(L,{children:e.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:e.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-xl w-[600px] shadow-2xl",children:[e.jsx("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-6 rounded-t-xl",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-adobe-text-primary",children:"Configure AI Provider"}),e.jsx("p",{className:"text-sm text-adobe-text-secondary mt-1",children:"Select and configure your AI provider"})]}),e.jsx("button",{onClick:a,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors p-1 hover:bg-adobe-bg-tertiary rounded-lg",children:e.jsx(A,{size:20})})]})}),e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Choose Provider"}),e.jsx(pt,{providers:g,value:c,onChange:d,placeholder:"Search and select a provider..."})]}),i&&e.jsxs(gt,{providerId:i.id,onRetry:()=>l(i.id),children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Select Model"}),i.isLoading?e.jsxs("div",{className:"flex items-center space-x-2 text-adobe-text-secondary p-4 bg-adobe-bg-secondary rounded-lg border border-adobe-border",children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-adobe-accent"}),e.jsx("span",{children:"Loading models..."})]}):i.error?e.jsx("div",{className:"p-4 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400 text-sm",children:i.error}):N.length>0?e.jsx(ht,{models:N,value:i.selectedModelId||"",onChange:h=>n(i.id,{selectedModelId:h}),placeholder:`Search ${i.name} models...`}):e.jsx("div",{className:"p-4 bg-adobe-bg-secondary rounded-lg border border-adobe-border text-adobe-text-secondary text-sm",children:"No models available. Configure the provider first."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:i.configType==="apiKey"?"API Key":"Base URL"}),e.jsx("input",{type:i.configType==="apiKey"?"password":"text",placeholder:i.configType==="apiKey"?`Enter your ${i.name} API key...`:`Enter ${i.name} base URL...`,value:i.configType==="apiKey"?i.apiKey||"":i.baseURL||"",onChange:h=>f(h.target.value),className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-lg px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-2 focus:ring-adobe-accent/20 outline-none transition-all"})]})]})]}),i&&e.jsx("div",{className:"border-t border-adobe-border p-6 bg-adobe-bg-secondary rounded-b-xl",children:e.jsxs("div",{className:"flex justify-end space-x-3",children:[e.jsx("button",{onClick:a,className:"px-4 py-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-lg transition-all",children:"Cancel"}),e.jsx("button",{onClick:p,disabled:!c||!i||i.configType==="apiKey"&&!i.apiKey||i.configType==="baseURL"&&!i.baseURL||!i.selectedModelId,className:"px-6 py-2 bg-adobe-accent text-white rounded-lg hover:bg-adobe-accent/90 transition-all disabled:opacity-50 disabled:cursor-not-allowed font-medium shadow-sm",children:"Save & Configure"})]})})]})})})},ee=$((a,o)=>({sessions:[],currentSessionId:null,isLoading:!1,error:null,loadHistory:async()=>{a({isLoading:!0,error:null});try{if(!window.CSInterface){const r=localStorage.getItem(S),n=r?JSON.parse(r):[];a({sessions:n,isLoading:!1});return}const s=await E("loadHistory()");if(s&&s.success&&s.data){const r=Array.isArray(s.data)?s.data:[];a({sessions:r,isLoading:!1})}else if(s&&s.success)a({sessions:[],isLoading:!1});else throw new Error(s?.message||"Failed to load history from ExtendScript")}catch(t){console.error("Failed to load history:",t);try{const s=localStorage.getItem(S),r=s?JSON.parse(s):[];a({sessions:r,isLoading:!1,error:`Using local storage fallback: ${t.message}`})}catch{a({error:t.message||"Failed to load chat history",isLoading:!1,sessions:[]})}}},saveSession:async t=>{try{a(n=>({sessions:n.sessions.some(c=>c.id===t.id)?n.sessions.map(c=>c.id===t.id?t:c):[...n.sessions,t]}));const s=o().sessions;if(!window.CSInterface){localStorage.setItem(S,JSON.stringify(s));return}await E(`saveHistory(${JSON.stringify(s)})`),localStorage.setItem(S,JSON.stringify(s))}catch(s){console.error("Failed to save session:",s);try{const r=o().sessions;localStorage.setItem(S,JSON.stringify(r))}catch{a({error:s.message||"Failed to save session"})}}},deleteSession:async t=>{try{a(n=>({sessions:n.sessions.filter(c=>c.id!==t),currentSessionId:n.currentSessionId===t?null:n.currentSessionId}));const s=o().sessions;if(!window.CSInterface){localStorage.setItem(S,JSON.stringify(s));return}await E(`saveHistory(${JSON.stringify(s)})`),localStorage.setItem(S,JSON.stringify(s))}catch(s){console.error("Failed to delete session:",s);try{const r=o().sessions;localStorage.setItem(S,JSON.stringify(r))}catch{a({error:s.message||"Failed to delete session"})}}},clearHistory:async()=>{try{if(a({sessions:[],currentSessionId:null}),!window.CSInterface){localStorage.setItem(S,JSON.stringify([]));return}await E("saveHistory([])"),localStorage.setItem(S,JSON.stringify([]))}catch(t){console.error("Failed to clear history:",t);try{localStorage.setItem(S,JSON.stringify([]))}catch{a({error:t.message||"Failed to clear history"})}}},createSession:t=>{const s={id:crypto.randomUUID(),title:t||`Chat ${new Date().toLocaleDateString()}`,messages:[],createdAt:Date.now(),updatedAt:Date.now()};a(n=>({sessions:[s,...n.sessions],currentSessionId:s.id}));const{saveSession:r}=o();return r(s),s},updateSession:(t,s)=>{a(r=>({sessions:r.sessions.map(n=>n.id===t?{...n,...s,updatedAt:Date.now()}:n)}))},setCurrentSession:t=>{a({currentSessionId:t})},getCurrentSession:()=>{const{sessions:t,currentSessionId:s}=o();return t.find(r=>r.id===s)||null},getSessionById:t=>{const{sessions:s}=o();return s.find(r=>r.id===t)||null},getSortedSessions:()=>{const{sessions:t}=o();return[...t].sort((s,r)=>r.updatedAt-s.updatedAt)}})),ae=Object.freeze(Object.defineProperty({__proto__:null,useHistoryStore:ee},Symbol.toStringTag,{value:"Module"})),yt=()=>{const{closeModal:a}=O(),{sessions:o}=ee(),[t,s]=x.useState({theme:"auto",autoSave:!0,showNotifications:!0,maxHistoryItems:100,debugMode:!1}),[r,n]=x.useState("settings"),[c,d]=x.useState(!1),[l,u]=x.useState(!0),[i,f]=x.useState("30d"),[p,N]=x.useState(!1);x.useEffect(()=>{(async()=>{try{const j=await T.load();j&&typeof j=="object"&&s(j)}catch(j){console.error("Failed to load settings:",j)}finally{u(!1)}})()},[]);const g=[{id:"settings",title:"General Settings",description:"Configure application preferences",icon:e.jsx(te,{size:16,className:"text-adobe-accent"})},{id:"analytics",title:"Analytics",description:"View usage statistics",icon:e.jsx(Le,{size:16,className:"text-adobe-accent"})},{id:"help",title:"Help & Support",description:"Get help and answers",icon:e.jsx(Pe,{size:16,className:"text-adobe-accent"})},{id:"about",title:"About",description:"About SahAI Extension",icon:e.jsx(q,{size:16,className:"text-adobe-accent"})}],m=(()=>{const v=Date.now(),j=o.filter(w=>{if(i==="all")return!0;const C=parseInt(i.replace("d",""));return v-w.createdAt<=C*24*60*60*1e3});return{messages:j.reduce((w,C)=>w+C.messages.length,0),sessions:j.length,tokens:j.reduce((w,C)=>w+(C.tokenCount||0),0),cost:j.reduce((w,C)=>w+(C.cost||0),0),avgLatency:j.length>0?j.reduce((w,C)=>w+(C.avgLatency||0),0)/j.length:0}})(),b=()=>{N(!0),setTimeout(()=>N(!1),800)},y=async()=>{d(!0);try{const j={...await T.load(),appSettings:t};await T.save(j),a()}catch(v){console.error("Failed to save settings:",v)}finally{d(!1)}};return e.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:e.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-lg w-[700px] h-[600px] shadow-2xl flex flex-col",children:[e.jsx("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-lg font-semibold text-adobe-text-primary",children:"Settings"}),e.jsx("button",{onClick:a,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(A,{size:20})})]})}),e.jsxs("div",{className:"flex-1 flex overflow-hidden",children:[e.jsx("div",{className:"w-1/3 border-r border-adobe-border bg-adobe-bg-secondary p-4 overflow-y-auto",children:e.jsx("div",{className:"space-y-1",children:g.map(v=>e.jsx("button",{onClick:()=>n(v.id),className:`w-full text-left p-3 rounded-md transition-colors ${r===v.id?"bg-adobe-accent/10 text-adobe-text-primary":"text-adobe-text-secondary hover:bg-adobe-bg-tertiary"}`,children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-1.5 rounded-md bg-adobe-bg-tertiary",children:v.icon}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-sm",children:v.title}),e.jsx("div",{className:"text-xs mt-1",children:v.description})]})]})},v.id))})}),e.jsx("div",{className:"w-2/3 p-6 overflow-y-auto",children:l?e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-adobe-accent"})}):r==="settings"?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-adobe-text-primary mb-3",children:"Appearance"}),e.jsx("div",{className:"space-y-3",children:e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-adobe-text-secondary mb-2",children:"Theme"}),e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:t.theme,onChange:v=>s(j=>({...j,theme:v.target.value})),className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-2 pr-8 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none appearance-none",children:[e.jsx("option",{value:"auto",children:"Auto (System)"}),e.jsx("option",{value:"light",children:"Light"}),e.jsx("option",{value:"dark",children:"Dark"})]}),e.jsx(R,{size:16,className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary pointer-events-none"})]})]})})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-adobe-text-primary mb-3",children:"General"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("label",{className:"flex items-center gap-3",children:[e.jsx("input",{type:"checkbox",checked:t.autoSave,onChange:v=>s(j=>({...j,autoSave:v.target.checked})),className:"w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"}),e.jsx("span",{className:"text-sm text-adobe-text-primary",children:"Auto-save conversations"})]}),e.jsxs("label",{className:"flex items-center gap-3",children:[e.jsx("input",{type:"checkbox",checked:t.showNotifications,onChange:v=>s(j=>({...j,showNotifications:v.target.checked})),className:"w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"}),e.jsx("span",{className:"text-sm text-adobe-text-primary",children:"Show notifications"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm text-adobe-text-secondary mb-2",children:["Max history items (",t.maxHistoryItems,")"]}),e.jsx("input",{type:"range",min:"10",max:"500",step:"10",value:t.maxHistoryItems,onChange:v=>s(j=>({...j,maxHistoryItems:parseInt(v.target.value)})),className:"w-full h-2 bg-adobe-bg-secondary rounded-lg appearance-none cursor-pointer"})]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-adobe-text-primary mb-3",children:"Advanced"}),e.jsx("div",{className:"space-y-3",children:e.jsxs("label",{className:"flex items-center gap-3",children:[e.jsx("input",{type:"checkbox",checked:t.debugMode,onChange:v=>s(j=>({...j,debugMode:v.target.checked})),className:"w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"}),e.jsx("span",{className:"text-sm text-adobe-text-primary",children:"Debug mode"})]})})]}),e.jsx("div",{className:"pt-4",children:e.jsxs("button",{onClick:y,disabled:c,className:"flex items-center gap-2 px-6 py-2 bg-adobe-accent text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-adobe-accent/90 transition-colors",children:[e.jsx(te,{size:16}),e.jsx("span",{children:c?"Saving...":"Save Settings"})]})})]}):r==="analytics"?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary",children:"Analytics"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:i,onChange:v=>f(v.target.value),className:"appearance-none bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-1.5 pr-8 text-sm text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none cursor-pointer",children:[e.jsx("option",{value:"7d",children:"Last 7 days"}),e.jsx("option",{value:"30d",children:"Last 30 days"}),e.jsx("option",{value:"90d",children:"Last 90 days"}),e.jsx("option",{value:"all",children:"All time"})]}),e.jsx(R,{size:14,className:"absolute right-2 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary pointer-events-none"})]}),e.jsx("button",{onClick:b,disabled:p,className:"p-1.5 text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(Q,{size:16,className:p?"animate-spin":""})})]})]}),p?e.jsx("div",{className:"grid grid-cols-2 gap-4",children:[...Array(4)].map((v,j)=>e.jsx("div",{className:"h-24 bg-adobe-bg-secondary rounded-md animate-pulse"},j))}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-1",children:"Messages"}),e.jsx("div",{className:"text-2xl font-medium text-adobe-text-primary",children:m.messages.toLocaleString()})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-1",children:"Sessions"}),e.jsx("div",{className:"text-2xl font-medium text-adobe-text-primary",children:m.sessions.toLocaleString()})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-1",children:"Tokens Used"}),e.jsx("div",{className:"text-2xl font-medium text-adobe-text-primary",children:m.tokens.toLocaleString()})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-1",children:"Est. Cost"}),e.jsxs("div",{className:"text-2xl font-medium text-adobe-text-primary",children:["$",m.cost.toFixed(4)]})]})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary",children:"Average Latency"}),e.jsxs("div",{className:"text-sm font-medium text-adobe-text-primary",children:[m.avgLatency.toFixed(2)," seconds"]})]}),e.jsx("div",{className:"h-2 bg-adobe-bg-tertiary rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-adobe-accent transition-all duration-300",style:{width:`${Math.min(100,m.avgLatency*50)}%`}})})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("h4",{className:"text-sm font-medium text-adobe-text-primary mb-2",children:"Performance Tips"}),e.jsxs("ul",{className:"text-sm text-adobe-text-secondary space-y-1",children:[e.jsx("li",{children:"• Use concise prompts to reduce token usage"}),e.jsx("li",{children:"• Select faster models for simple tasks"}),e.jsx("li",{children:"• Monitor usage to optimize costs"})]})]})]})]}):r==="help"?e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary mb-2",children:"Help & Support"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"p-4 bg-adobe-bg-secondary rounded-md",children:[e.jsx("h4",{className:"font-medium text-adobe-text-primary mb-2",children:"Documentation"}),e.jsx("p",{className:"text-sm text-adobe-text-secondary",children:"Read our comprehensive documentation for detailed guides."})]}),e.jsxs("div",{className:"p-4 bg-adobe-bg-secondary rounded-md",children:[e.jsx("h4",{className:"font-medium text-adobe-text-primary mb-2",children:"FAQ"}),e.jsx("p",{className:"text-sm text-adobe-text-secondary",children:"Find answers to frequently asked questions."})]}),e.jsxs("div",{className:"p-4 bg-adobe-bg-secondary rounded-md",children:[e.jsx("h4",{className:"font-medium text-adobe-text-primary mb-2",children:"Contact Support"}),e.jsx("p",{className:"text-sm text-adobe-text-secondary",children:"Email <NAME_EMAIL> for assistance."})]})]})]}):e.jsx("div",{className:"flex flex-col items-center justify-center h-full text-adobe-text-secondary",children:e.jsxs("div",{className:"text-center",children:[e.jsx(q,{size:48,className:"mx-auto mb-4 opacity-50"}),e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary mb-2",children:"About SahAI"}),e.jsx("p",{className:"text-sm mb-4",children:"Version 2.0.0"}),e.jsx("p",{className:"text-sm",children:"AI-powered assistant for Adobe Creative Suite"})]})})})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary border-t border-adobe-border p-3 text-xs text-adobe-text-secondary text-center",children:[r==="settings"&&"General Settings",r==="analytics"&&"Usage Analytics",r==="help"&&"Help & Support",r==="about"&&"About SahAI"]})]})})},vt=()=>{const{closeModal:a}=O(),{sessions:o,isLoading:t,error:s,loadHistory:r,deleteSession:n,getSortedSessions:c}=ee(),[d,l]=x.useState(""),[u,i]=x.useState(null),[f,p]=x.useState("recent");x.useEffect(()=>{r()},[r]);const N=c().filter(b=>b.title.toLowerCase().includes(d.toLowerCase())||b.messages.some(y=>y.content.toLowerCase().includes(d.toLowerCase()))).sort((b,y)=>f==="alphabetical"?b.title.localeCompare(y.title):f==="oldest"?b.createdAt-y.createdAt:y.createdAt-b.createdAt),g=async(b,y)=>{y.stopPropagation(),confirm("Are you sure you want to delete this chat session?")&&await n(b)},h=b=>{const y=new Date(b),j=(new Date().getTime()-y.getTime())/(1e3*60*60);return j<24?y.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):j<24*7?y.toLocaleDateString([],{weekday:"short",hour:"2-digit",minute:"2-digit"}):y.toLocaleDateString([],{month:"short",day:"numeric",year:"numeric"})},m=b=>{const y=b.messages[b.messages.length-1];if(!y)return"No messages";const v=y.content.slice(0,100);return v.length<y.content.length?`${v}...`:v};return e.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:e.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-lg w-[700px] h-[600px] shadow-2xl flex flex-col",children:[e.jsx("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-lg font-semibold text-adobe-text-primary",children:"Chat History"}),e.jsx("button",{onClick:a,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(A,{size:20})})]})}),e.jsx("div",{className:"p-4 border-b border-adobe-border",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx("input",{type:"text",value:d,onChange:b=>l(b.target.value),placeholder:"Search chat history...",className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-2 pr-10 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none text-sm"}),e.jsx("button",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary hover:text-adobe-text-primary",onClick:()=>l(""),children:d?e.jsx(A,{size:16}):e.jsx(ne,{size:16})})]}),e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:f,onChange:b=>p(b.target.value),className:"appearance-none bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-2 pl-3 pr-8 text-adobe-text-primary text-sm focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none cursor-pointer",children:[e.jsx("option",{value:"recent",children:"Most Recent"}),e.jsx("option",{value:"oldest",children:"Oldest First"}),e.jsx("option",{value:"alphabetical",children:"Alphabetical"})]}),e.jsx(R,{size:16,className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary pointer-events-none"})]})]})}),e.jsx("div",{className:"flex-1 overflow-hidden",children:t?e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-adobe-accent"})}):s?e.jsxs("div",{className:"p-4 text-center",children:[e.jsxs("div",{className:"bg-red-900/20 border border-red-800/50 rounded-lg p-4 mb-4",children:[e.jsx("p",{className:"text-red-400 font-medium mb-2",children:"Error loading history:"}),e.jsx("p",{className:"text-sm text-red-300",children:s})]}),e.jsx("button",{onClick:r,className:"px-4 py-2 bg-adobe-accent hover:bg-adobe-accent-hover text-white rounded-md transition-colors",children:"Retry Loading History"})]}):N.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-adobe-text-secondary gap-2",children:[e.jsx(G,{size:48,className:"opacity-50"}),e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary",children:d?"No matching sessions found":"No chat history yet"}),e.jsx("p",{className:"text-sm",children:d?"Try a different search term":"Start a new conversation to see it here"})]}):e.jsx("div",{className:"h-full overflow-y-auto p-2 space-y-2",children:N.map(b=>e.jsxs("div",{className:`p-3 rounded-md cursor-pointer transition-colors ${u?.id===b.id?"bg-adobe-accent/10 border-l-2 border-adobe-accent":"bg-adobe-bg-secondary hover:bg-adobe-bg-tertiary"}`,onClick:()=>i(b),children:[e.jsxs("div",{className:"flex justify-between items-start gap-2",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"font-medium text-adobe-text-primary truncate",children:b.title}),e.jsx("p",{className:"text-sm text-adobe-text-secondary mt-1 line-clamp-2",children:m(b)})]}),e.jsx("button",{onClick:y=>g(b.id,y),className:"text-adobe-text-secondary hover:text-adobe-error transition-colors p-1",title:"Delete session",children:e.jsx(Re,{size:14})})]}),e.jsxs("div",{className:"flex justify-between items-center mt-2",children:[e.jsxs("div",{className:"flex items-center gap-1 text-xs text-adobe-text-secondary",children:[e.jsx(G,{size:12}),e.jsxs("span",{children:[b.messages.length," messages"]})]}),e.jsxs("div",{className:"flex items-center gap-1 text-xs text-adobe-text-secondary",children:[e.jsx(Ae,{size:12}),e.jsx("span",{children:h(b.createdAt)})]})]})]},b.id))})}),e.jsxs("div",{className:"p-3 border-t border-adobe-border text-xs text-adobe-text-secondary text-center",children:["Showing ",N.length," of ",o.length," chat sessions"]})]})})},jt=()=>{const{closeModal:a}=O(),{getActiveProvider:o}=M(),[t,s]=x.useState({isOnline:null,isChecking:!1}),r=o(),n=async()=>{if(!r?.isConfigured){s({isOnline:null,isChecking:!1});return}s(i=>({...i,isChecking:!0,error:void 0}));try{const i=await X.checkProviderStatus(r.id,{apiKey:r.apiKey,baseURL:r.baseURL});s({isOnline:i.isOnline,latency:i.latency,isChecking:!1,lastChecked:Date.now()})}catch(i){s({isOnline:!1,isChecking:!1,error:i.message,lastChecked:Date.now()})}};x.useEffect(()=>{n();const i=setInterval(n,3e4);return()=>clearInterval(i)},[r]);const c=()=>t.isChecking?e.jsx(Y,{size:20,className:"animate-spin text-yellow-500"}):t.isOnline===!0?e.jsx(Me,{size:20,className:"text-green-500"}):t.isOnline===!1?e.jsx(Oe,{size:20,className:"text-red-500"}):e.jsx(J,{size:20,className:"text-gray-500"}),d=()=>t.isChecking?"Checking connection...":t.isOnline===!0?"Online":t.isOnline===!1?"Offline":"Unknown",l=()=>t.isChecking?"text-yellow-600":t.isOnline===!0?"text-green-600":t.isOnline===!1?"text-red-600":"text-gray-600",u=()=>t.isOnline===!0?"good":t.isOnline===!1?"critical":"warning";return e.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:e.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-lg w-[700px] h-[600px] shadow-2xl flex flex-col",children:[e.jsx("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-lg font-semibold text-adobe-text-primary",children:"Provider Status"}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("button",{onClick:()=>n(),disabled:t.isChecking,className:"p-1 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded transition-colors disabled:opacity-50",title:"Refresh status",children:e.jsx(Q,{size:18,className:t.isChecking?"animate-spin":""})}),e.jsx("button",{onClick:a,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(A,{size:20})})]})]})}),e.jsx("div",{className:"flex-1 overflow-hidden p-4",children:r?e.jsxs("div",{className:"h-full flex flex-col gap-4",children:[e.jsx("div",{className:"bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex-shrink-0",children:c()}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-adobe-text-primary",children:r.name}),e.jsx("p",{className:`text-sm font-medium ${l()}`,children:d()})]})]}),e.jsx("div",{className:`text-xs px-2 py-1 rounded ${u()==="good"?"bg-green-900/30 text-green-500":u()==="warning"?"bg-yellow-900/30 text-yellow-500":"bg-red-900/30 text-red-500"}`,children:d()})]})}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-adobe-text-secondary",children:"Latency"}),e.jsx("span",{className:"text-xs text-adobe-text-tertiary",children:"Lower is better"})]}),e.jsx("div",{className:"mt-2",children:t.latency?e.jsxs("div",{className:"flex items-end gap-2",children:[e.jsxs("span",{className:"text-2xl font-medium text-adobe-text-primary",children:[t.latency,"ms"]}),e.jsx("span",{className:`text-xs mb-1 ${t.latency<100?"text-green-500":t.latency<300?"text-yellow-500":"text-red-500"}`,children:t.latency<100?"Excellent":t.latency<300?"Good":"Poor"})]}):e.jsx("span",{className:"text-adobe-text-tertiary",children:"--"})})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary",children:"Last Checked"}),e.jsx("div",{className:"mt-2",children:t.lastChecked?e.jsxs("div",{className:"text-adobe-text-primary",children:[e.jsx("div",{className:"text-xl font-medium",children:new Date(t.lastChecked).toLocaleTimeString()}),e.jsx("div",{className:"text-xs text-adobe-text-tertiary mt-1",children:new Date(t.lastChecked).toLocaleDateString()})]}):e.jsx("span",{className:"text-adobe-text-tertiary",children:"--"})})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border col-span-2",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary",children:"Endpoint"}),e.jsx("div",{className:"mt-2",children:r.baseURL?e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"truncate text-adobe-text-primary font-mono text-sm",children:r.baseURL}),e.jsx("button",{className:"text-xs text-adobe-accent hover:text-adobe-accent-hover",onClick:()=>navigator.clipboard.writeText(r.baseURL||""),children:"Copy"})]}):e.jsx("span",{className:"text-adobe-text-tertiary",children:"Not configured"})})]})]}),e.jsxs("div",{className:"flex-1 bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border overflow-auto",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-2",children:"Status Details"}),t.error?e.jsxs("div",{className:"p-3 bg-red-900/20 border border-red-800/50 rounded text-sm text-red-400",children:[e.jsx("div",{className:"font-medium mb-1",children:"Error:"}),e.jsx("div",{children:t.error})]}):e.jsx("div",{className:"text-sm text-adobe-text-primary",children:t.isChecking?"Checking provider status...":t.isOnline===!0?"Provider is online and responding normally.":t.isOnline===!1?"Provider is offline or not responding to requests.":"Provider status unknown. Please check configuration."})]})]}):e.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-adobe-text-secondary gap-2",children:[e.jsx(J,{size:48,className:"opacity-50"}),e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary",children:"No provider selected"}),e.jsx("p",{className:"text-sm",children:"Select a provider to check connection status"})]})}),e.jsx("div",{className:"p-3 border-t border-adobe-border text-xs text-adobe-text-secondary text-center bg-adobe-bg-secondary",children:"Status checks are performed automatically every 30 seconds"})]})})},Nt=()=>{const{modal:a}=O();if(!a)return null;switch(a){case"provider":return e.jsx(ft,{});case"settings":return e.jsx(yt,{});case"chat-history":return e.jsx(vt,{});case"status":return e.jsx(jt,{});default:return null}},St=({toast:a})=>{const{removeToast:o}=k(),[t,s]=x.useState(!1),r=()=>{s(!0),setTimeout(()=>o(a.id),200)};x.useEffect(()=>{const d=setTimeout(()=>{r()},a.duration||4e3);return()=>clearTimeout(d)},[a.duration]);const n=()=>{switch(a.type){case"success":return e.jsx(_e,{size:20,className:"text-adobe-success"});case"error":return e.jsx(J,{size:20,className:"text-adobe-error"});case"warning":return e.jsx(ie,{size:20,className:"text-adobe-warning"});case"info":default:return e.jsx(q,{size:20,className:"text-adobe-accent"})}},c=()=>{switch(a.type){case"success":return"border-l-adobe-success";case"error":return"border-l-adobe-error";case"warning":return"border-l-adobe-warning";case"info":default:return"border-l-adobe-accent"}};return e.jsx("div",{className:`
        transform transition-all duration-200 ease-in-out
        ${t?"translate-x-full opacity-0":"translate-x-0 opacity-100"}
        bg-adobe-bg-secondary border border-adobe-border ${c()} border-l-4
        rounded-md shadow-lg p-4 mb-3 max-w-sm w-full
      `,children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"flex-shrink-0 mt-0.5",children:n()}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"text-sm font-medium text-adobe-text-primary truncate",children:a.title}),a.message&&e.jsx("p",{className:"text-xs text-adobe-text-secondary mt-1 break-words",children:a.message})]}),e.jsx("button",{onClick:r,className:"flex-shrink-0 text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(A,{size:16})})]})})},wt=()=>{const{toasts:a}=k();return a.length===0?null:e.jsx("div",{className:"fixed top-4 right-4 z-[9999] pointer-events-none",children:e.jsx("div",{className:"space-y-2 pointer-events-auto",children:a.map(o=>e.jsx(St,{toast:o},o.id))})})},Ct=()=>e.jsxs("div",{className:"flex flex-col h-screen bg-adobe-bg text-adobe-text font-sans",children:[e.jsx(L,{children:e.jsx(ot,{})}),e.jsx(L,{children:e.jsx(xt,{})}),e.jsx(L,{children:e.jsx(bt,{})}),e.jsx(L,{children:e.jsx(Nt,{})}),e.jsx(L,{children:e.jsx(wt,{})})]});le();k.getState();M.getState().loadSettings().then(()=>{V.createRoot(document.getElementById("root")).render(e.jsx(oe.StrictMode,{children:e.jsx(Ct,{})}))});export{Lt as D,Mt as E,At as L,Rt as M,Pt as S};
