import{D as o,S as a,L as e,E as n,M as t}from"./index-DaZHrgDt.js";import"./vendor-DsceW-4w.js";import"./shiki-Dr4f9TeX.js";import"./ui-B3AhFxSy.js";const d={openai:[{id:"gpt-4o",name:"GPT-4o",description:"Most capable OpenAI model",contextLength:e,isRecommended:!0},{id:"gpt-4o-mini",name:"GPT-4o Mini",description:"Faster, more affordable",contextLength:e,isRecommended:!1},{id:"gpt-4-turbo",name:"GPT-4 Turbo",description:"Previous generation flagship",contextLength:e,isRecommended:!1},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo",description:"Legacy model",contextLength:16384,isRecommended:!1}],anthropic:[{id:"claude-3-5-sonnet-20241022",name:"Claude 3.5 Sonnet",description:"Anthropic's most capable model",contextLength:n,isRecommended:!0},{id:"claude-3-5-haiku-20241022",name:"Claude 3.5 Haiku",description:"Fast and efficient",contextLength:n,isRecommended:!1},{id:"claude-3-opus-20240229",name:"Claude 3 Opus",description:"Powerful reasoning",contextLength:n,isRecommended:!1},{id:"claude-2.1",name:"Claude 2.1",description:"Previous generation",contextLength:n,isRecommended:!1}],gemini:[{id:"gemini-1.5-pro",name:"Gemini 1.5 Pro",description:"Google's most capable model",contextLength:2e6,isRecommended:!0},{id:"gemini-1.5-flash",name:"Gemini 1.5 Flash",description:"Fast and efficient",contextLength:1e6,isRecommended:!1},{id:"gemini-1.0-pro",name:"Gemini 1.0 Pro",description:"Previous generation",contextLength:t,isRecommended:!1}],groq:[{id:"llama-3.1-70b-versatile",name:"Llama 3.1 70B",description:"Balanced performance",contextLength:131072,isRecommended:!0},{id:"llama-3.1-8b-instant",name:"Llama 3.1 8B",description:"Fast inference",contextLength:131072,isRecommended:!1},{id:"mixtral-8x7b-32768",name:"Mixtral 8x7B",description:"Large context",contextLength:t,isRecommended:!1}],deepseek:[{id:"deepseek-chat",name:"DeepSeek Chat",description:"General purpose",contextLength:e,isRecommended:!0},{id:"deepseek-coder",name:"DeepSeek Coder",description:"Code-focused",contextLength:e,isRecommended:!1}],openrouter:[{id:"openai/gpt-4o",name:"GPT-4o (OpenRouter)",description:"OpenAI via OpenRouter",contextLength:e,isRecommended:!0},{id:"anthropic/claude-3.5-sonnet",name:"Claude 3.5 Sonnet (OpenRouter)",description:"Anthropic via OpenRouter",contextLength:n,isRecommended:!1},{id:"meta-llama/llama-3.1-70b-instruct",name:"Llama 3.1 70B (OpenRouter)",description:"Meta via OpenRouter",contextLength:131072,isRecommended:!1}],ollama:[{id:"llama3.1",name:"Llama 3.1",description:"Open source LLM",contextLength:o,isRecommended:!0},{id:"mistral",name:"Mistral",description:"Efficient transformer",contextLength:a,isRecommended:!1},{id:"codellama",name:"Code Llama",description:"Code-focused",contextLength:16384,isRecommended:!1}]};function l(i){return d[i]||[]}export{d as FALLBACK_MODELS,l as getFallbackModels};
