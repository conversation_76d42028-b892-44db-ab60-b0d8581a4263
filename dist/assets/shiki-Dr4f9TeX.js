var yr=Object.defineProperty;var Er=(r,e,t)=>e in r?yr(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var h=(r,e,t)=>Er(r,typeof e!="symbol"?e+"":e,t);const vr="modulepreload",Rr=function(r,e){return new URL(r,e).href},st={},u=function(e,t,n){let i=Promise.resolve();if(t&&t.length>0){const a=document.getElementsByTagName("link"),l=document.querySelector("meta[property=csp-nonce]"),s=l?.nonce||l?.getAttribute("nonce");i=Promise.allSettled(t.map(c=>{if(c=Rr(c,n),c in st)return;st[c]=!0;const m=c.endsWith(".css"),d=m?'[rel="stylesheet"]':"";if(!!n)for(let f=a.length-1;f>=0;f--){const b=a[f];if(b.href===c&&(!m||b.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${c}"]${d}`))return;const p=document.createElement("link");if(p.rel=m?"stylesheet":vr,m||(p.as="script"),p.crossOrigin="",p.href=c,s&&p.setAttribute("nonce",s),document.head.appendChild(p),m)return new Promise((f,b)=>{p.addEventListener("load",f),p.addEventListener("error",()=>b(new Error(`Unable to preload CSS for ${c}`)))})}))}function o(a){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=a,window.dispatchEvent(l),!l.defaultPrevented)throw a}return i.then(a=>{for(const l of a||[])l.status==="rejected"&&o(l.reason);return e().catch(o)})},Ct=[{id:"abap",name:"ABAP",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/abap.mjs"),[],import.meta.url)},{id:"actionscript-3",name:"ActionScript",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/actionscript-3.mjs"),[],import.meta.url)},{id:"ada",name:"Ada",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/ada.mjs"),[],import.meta.url)},{id:"angular-html",name:"Angular HTML",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/angular-html.mjs"),[],import.meta.url)},{id:"angular-ts",name:"Angular TypeScript",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/angular-ts.mjs"),[],import.meta.url)},{id:"apache",name:"Apache Conf",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/apache.mjs"),[],import.meta.url)},{id:"apex",name:"Apex",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/apex.mjs"),[],import.meta.url)},{id:"apl",name:"APL",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/apl.mjs"),[],import.meta.url)},{id:"applescript",name:"AppleScript",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/applescript.mjs"),[],import.meta.url)},{id:"ara",name:"Ara",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/ara.mjs"),[],import.meta.url)},{id:"asciidoc",name:"AsciiDoc",aliases:["adoc"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/asciidoc.mjs"),[],import.meta.url)},{id:"asm",name:"Assembly",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/asm.mjs"),[],import.meta.url)},{id:"astro",name:"Astro",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/astro.mjs"),[],import.meta.url)},{id:"awk",name:"AWK",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/awk.mjs"),[],import.meta.url)},{id:"ballerina",name:"Ballerina",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/ballerina.mjs"),[],import.meta.url)},{id:"bat",name:"Batch File",aliases:["batch"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/bat.mjs"),[],import.meta.url)},{id:"beancount",name:"Beancount",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/beancount.mjs"),[],import.meta.url)},{id:"berry",name:"Berry",aliases:["be"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/berry.mjs"),[],import.meta.url)},{id:"bibtex",name:"BibTeX",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/bibtex.mjs"),[],import.meta.url)},{id:"bicep",name:"Bicep",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/bicep.mjs"),[],import.meta.url)},{id:"blade",name:"Blade",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/blade.mjs"),[],import.meta.url)},{id:"bsl",name:"1C (Enterprise)",aliases:["1c"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/bsl.mjs"),[],import.meta.url)},{id:"c",name:"C",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/c.mjs"),[],import.meta.url)},{id:"cadence",name:"Cadence",aliases:["cdc"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/cadence.mjs"),[],import.meta.url)},{id:"cairo",name:"Cairo",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/cairo.mjs"),[],import.meta.url)},{id:"clarity",name:"Clarity",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/clarity.mjs"),[],import.meta.url)},{id:"clojure",name:"Clojure",aliases:["clj"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/clojure.mjs"),[],import.meta.url)},{id:"cmake",name:"CMake",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/cmake.mjs"),[],import.meta.url)},{id:"cobol",name:"COBOL",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/cobol.mjs"),[],import.meta.url)},{id:"codeowners",name:"CODEOWNERS",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/codeowners.mjs"),[],import.meta.url)},{id:"codeql",name:"CodeQL",aliases:["ql"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/codeql.mjs"),[],import.meta.url)},{id:"coffee",name:"CoffeeScript",aliases:["coffeescript"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/coffee.mjs"),[],import.meta.url)},{id:"common-lisp",name:"Common Lisp",aliases:["lisp"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/common-lisp.mjs"),[],import.meta.url)},{id:"coq",name:"Coq",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/coq.mjs"),[],import.meta.url)},{id:"cpp",name:"C++",aliases:["c++"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/cpp.mjs"),[],import.meta.url)},{id:"crystal",name:"Crystal",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/crystal.mjs"),[],import.meta.url)},{id:"csharp",name:"C#",aliases:["c#","cs"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/csharp.mjs"),[],import.meta.url)},{id:"css",name:"CSS",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/css.mjs"),[],import.meta.url)},{id:"csv",name:"CSV",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/csv.mjs"),[],import.meta.url)},{id:"cue",name:"CUE",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/cue.mjs"),[],import.meta.url)},{id:"cypher",name:"Cypher",aliases:["cql"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/cypher.mjs"),[],import.meta.url)},{id:"d",name:"D",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/d.mjs"),[],import.meta.url)},{id:"dart",name:"Dart",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/dart.mjs"),[],import.meta.url)},{id:"dax",name:"DAX",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/dax.mjs"),[],import.meta.url)},{id:"desktop",name:"Desktop",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/desktop.mjs"),[],import.meta.url)},{id:"diff",name:"Diff",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/diff.mjs"),[],import.meta.url)},{id:"docker",name:"Dockerfile",aliases:["dockerfile"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/docker.mjs"),[],import.meta.url)},{id:"dotenv",name:"dotEnv",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/dotenv.mjs"),[],import.meta.url)},{id:"dream-maker",name:"Dream Maker",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/dream-maker.mjs"),[],import.meta.url)},{id:"edge",name:"Edge",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/edge.mjs"),[],import.meta.url)},{id:"elixir",name:"Elixir",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/elixir.mjs"),[],import.meta.url)},{id:"elm",name:"Elm",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/elm.mjs"),[],import.meta.url)},{id:"emacs-lisp",name:"Emacs Lisp",aliases:["elisp"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/emacs-lisp.mjs"),[],import.meta.url)},{id:"erb",name:"ERB",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/erb.mjs"),[],import.meta.url)},{id:"erlang",name:"Erlang",aliases:["erl"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/erlang.mjs"),[],import.meta.url)},{id:"fennel",name:"Fennel",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/fennel.mjs"),[],import.meta.url)},{id:"fish",name:"Fish",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/fish.mjs"),[],import.meta.url)},{id:"fluent",name:"Fluent",aliases:["ftl"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/fluent.mjs"),[],import.meta.url)},{id:"fortran-fixed-form",name:"Fortran (Fixed Form)",aliases:["f","for","f77"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/fortran-fixed-form.mjs"),[],import.meta.url)},{id:"fortran-free-form",name:"Fortran (Free Form)",aliases:["f90","f95","f03","f08","f18"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/fortran-free-form.mjs"),[],import.meta.url)},{id:"fsharp",name:"F#",aliases:["f#","fs"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/fsharp.mjs"),[],import.meta.url)},{id:"gdresource",name:"GDResource",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/gdresource.mjs"),[],import.meta.url)},{id:"gdscript",name:"GDScript",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/gdscript.mjs"),[],import.meta.url)},{id:"gdshader",name:"GDShader",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/gdshader.mjs"),[],import.meta.url)},{id:"genie",name:"Genie",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/genie.mjs"),[],import.meta.url)},{id:"gherkin",name:"Gherkin",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/gherkin.mjs"),[],import.meta.url)},{id:"git-commit",name:"Git Commit Message",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/git-commit.mjs"),[],import.meta.url)},{id:"git-rebase",name:"Git Rebase Message",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/git-rebase.mjs"),[],import.meta.url)},{id:"gleam",name:"Gleam",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/gleam.mjs"),[],import.meta.url)},{id:"glimmer-js",name:"Glimmer JS",aliases:["gjs"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/glimmer-js.mjs"),[],import.meta.url)},{id:"glimmer-ts",name:"Glimmer TS",aliases:["gts"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/glimmer-ts.mjs"),[],import.meta.url)},{id:"glsl",name:"GLSL",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/glsl.mjs"),[],import.meta.url)},{id:"gnuplot",name:"Gnuplot",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/gnuplot.mjs"),[],import.meta.url)},{id:"go",name:"Go",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/go.mjs"),[],import.meta.url)},{id:"graphql",name:"GraphQL",aliases:["gql"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/graphql.mjs"),[],import.meta.url)},{id:"groovy",name:"Groovy",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/groovy.mjs"),[],import.meta.url)},{id:"hack",name:"Hack",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/hack.mjs"),[],import.meta.url)},{id:"haml",name:"Ruby Haml",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/haml.mjs"),[],import.meta.url)},{id:"handlebars",name:"Handlebars",aliases:["hbs"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/handlebars.mjs"),[],import.meta.url)},{id:"haskell",name:"Haskell",aliases:["hs"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/haskell.mjs"),[],import.meta.url)},{id:"haxe",name:"Haxe",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/haxe.mjs"),[],import.meta.url)},{id:"hcl",name:"HashiCorp HCL",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/hcl.mjs"),[],import.meta.url)},{id:"hjson",name:"Hjson",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/hjson.mjs"),[],import.meta.url)},{id:"hlsl",name:"HLSL",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/hlsl.mjs"),[],import.meta.url)},{id:"html",name:"HTML",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/html.mjs"),[],import.meta.url)},{id:"html-derivative",name:"HTML (Derivative)",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/html-derivative.mjs"),[],import.meta.url)},{id:"http",name:"HTTP",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/http.mjs"),[],import.meta.url)},{id:"hxml",name:"HXML",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/hxml.mjs"),[],import.meta.url)},{id:"hy",name:"Hy",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/hy.mjs"),[],import.meta.url)},{id:"imba",name:"Imba",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/imba.mjs"),[],import.meta.url)},{id:"ini",name:"INI",aliases:["properties"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/ini.mjs"),[],import.meta.url)},{id:"java",name:"Java",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/java.mjs"),[],import.meta.url)},{id:"javascript",name:"JavaScript",aliases:["js"],import:()=>u(()=>import("./javascript-ySlJ1b_l.js"),[],import.meta.url)},{id:"jinja",name:"Jinja",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/jinja.mjs"),[],import.meta.url)},{id:"jison",name:"Jison",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/jison.mjs"),[],import.meta.url)},{id:"json",name:"JSON",import:()=>u(()=>import("./json-BQoSv7ci.js"),[],import.meta.url)},{id:"json5",name:"JSON5",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/json5.mjs"),[],import.meta.url)},{id:"jsonc",name:"JSON with Comments",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/jsonc.mjs"),[],import.meta.url)},{id:"jsonl",name:"JSON Lines",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/jsonl.mjs"),[],import.meta.url)},{id:"jsonnet",name:"Jsonnet",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/jsonnet.mjs"),[],import.meta.url)},{id:"jssm",name:"JSSM",aliases:["fsl"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/jssm.mjs"),[],import.meta.url)},{id:"jsx",name:"JSX",import:()=>u(()=>import("./jsx-BAng5TT0.js"),[],import.meta.url)},{id:"julia",name:"Julia",aliases:["jl"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/julia.mjs"),[],import.meta.url)},{id:"kotlin",name:"Kotlin",aliases:["kt","kts"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/kotlin.mjs"),[],import.meta.url)},{id:"kusto",name:"Kusto",aliases:["kql"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/kusto.mjs"),[],import.meta.url)},{id:"latex",name:"LaTeX",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/latex.mjs"),[],import.meta.url)},{id:"lean",name:"Lean 4",aliases:["lean4"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/lean.mjs"),[],import.meta.url)},{id:"less",name:"Less",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/less.mjs"),[],import.meta.url)},{id:"liquid",name:"Liquid",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/liquid.mjs"),[],import.meta.url)},{id:"log",name:"Log file",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/log.mjs"),[],import.meta.url)},{id:"logo",name:"Logo",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/logo.mjs"),[],import.meta.url)},{id:"lua",name:"Lua",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/lua.mjs"),[],import.meta.url)},{id:"luau",name:"Luau",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/luau.mjs"),[],import.meta.url)},{id:"make",name:"Makefile",aliases:["makefile"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/make.mjs"),[],import.meta.url)},{id:"markdown",name:"Markdown",aliases:["md"],import:()=>u(()=>import("./markdown-UIAJJxZW.js"),[],import.meta.url)},{id:"marko",name:"Marko",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/marko.mjs"),[],import.meta.url)},{id:"matlab",name:"MATLAB",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/matlab.mjs"),[],import.meta.url)},{id:"mdc",name:"MDC",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/mdc.mjs"),[],import.meta.url)},{id:"mdx",name:"MDX",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/mdx.mjs"),[],import.meta.url)},{id:"mermaid",name:"Mermaid",aliases:["mmd"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/mermaid.mjs"),[],import.meta.url)},{id:"mipsasm",name:"MIPS Assembly",aliases:["mips"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/mipsasm.mjs"),[],import.meta.url)},{id:"mojo",name:"Mojo",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/mojo.mjs"),[],import.meta.url)},{id:"move",name:"Move",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/move.mjs"),[],import.meta.url)},{id:"narrat",name:"Narrat Language",aliases:["nar"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/narrat.mjs"),[],import.meta.url)},{id:"nextflow",name:"Nextflow",aliases:["nf"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/nextflow.mjs"),[],import.meta.url)},{id:"nginx",name:"Nginx",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/nginx.mjs"),[],import.meta.url)},{id:"nim",name:"Nim",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/nim.mjs"),[],import.meta.url)},{id:"nix",name:"Nix",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/nix.mjs"),[],import.meta.url)},{id:"nushell",name:"nushell",aliases:["nu"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/nushell.mjs"),[],import.meta.url)},{id:"objective-c",name:"Objective-C",aliases:["objc"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/objective-c.mjs"),[],import.meta.url)},{id:"objective-cpp",name:"Objective-C++",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/objective-cpp.mjs"),[],import.meta.url)},{id:"ocaml",name:"OCaml",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/ocaml.mjs"),[],import.meta.url)},{id:"pascal",name:"Pascal",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/pascal.mjs"),[],import.meta.url)},{id:"perl",name:"Perl",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/perl.mjs"),[],import.meta.url)},{id:"php",name:"PHP",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/php.mjs"),[],import.meta.url)},{id:"plsql",name:"PL/SQL",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/plsql.mjs"),[],import.meta.url)},{id:"po",name:"Gettext PO",aliases:["pot","potx"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/po.mjs"),[],import.meta.url)},{id:"polar",name:"Polar",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/polar.mjs"),[],import.meta.url)},{id:"postcss",name:"PostCSS",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/postcss.mjs"),[],import.meta.url)},{id:"powerquery",name:"PowerQuery",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/powerquery.mjs"),[],import.meta.url)},{id:"powershell",name:"PowerShell",aliases:["ps","ps1"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/powershell.mjs"),[],import.meta.url)},{id:"prisma",name:"Prisma",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/prisma.mjs"),[],import.meta.url)},{id:"prolog",name:"Prolog",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/prolog.mjs"),[],import.meta.url)},{id:"proto",name:"Protocol Buffer 3",aliases:["protobuf"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/proto.mjs"),[],import.meta.url)},{id:"pug",name:"Pug",aliases:["jade"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/pug.mjs"),[],import.meta.url)},{id:"puppet",name:"Puppet",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/puppet.mjs"),[],import.meta.url)},{id:"purescript",name:"PureScript",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/purescript.mjs"),[],import.meta.url)},{id:"python",name:"Python",aliases:["py"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/python.mjs"),[],import.meta.url)},{id:"qml",name:"QML",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/qml.mjs"),[],import.meta.url)},{id:"qmldir",name:"QML Directory",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/qmldir.mjs"),[],import.meta.url)},{id:"qss",name:"Qt Style Sheets",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/qss.mjs"),[],import.meta.url)},{id:"r",name:"R",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/r.mjs"),[],import.meta.url)},{id:"racket",name:"Racket",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/racket.mjs"),[],import.meta.url)},{id:"raku",name:"Raku",aliases:["perl6"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/raku.mjs"),[],import.meta.url)},{id:"razor",name:"ASP.NET Razor",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/razor.mjs"),[],import.meta.url)},{id:"reg",name:"Windows Registry Script",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/reg.mjs"),[],import.meta.url)},{id:"regexp",name:"RegExp",aliases:["regex"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/regexp.mjs"),[],import.meta.url)},{id:"rel",name:"Rel",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/rel.mjs"),[],import.meta.url)},{id:"riscv",name:"RISC-V",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/riscv.mjs"),[],import.meta.url)},{id:"rst",name:"reStructuredText",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/rst.mjs"),[],import.meta.url)},{id:"ruby",name:"Ruby",aliases:["rb"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/ruby.mjs"),[],import.meta.url)},{id:"rust",name:"Rust",aliases:["rs"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/rust.mjs"),[],import.meta.url)},{id:"sas",name:"SAS",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/sas.mjs"),[],import.meta.url)},{id:"sass",name:"Sass",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/sass.mjs"),[],import.meta.url)},{id:"scala",name:"Scala",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/scala.mjs"),[],import.meta.url)},{id:"scheme",name:"Scheme",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/scheme.mjs"),[],import.meta.url)},{id:"scss",name:"SCSS",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/scss.mjs"),[],import.meta.url)},{id:"sdbl",name:"1C (Query)",aliases:["1c-query"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/sdbl.mjs"),[],import.meta.url)},{id:"shaderlab",name:"ShaderLab",aliases:["shader"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/shaderlab.mjs"),[],import.meta.url)},{id:"shellscript",name:"Shell",aliases:["bash","sh","shell","zsh"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/shellscript.mjs"),[],import.meta.url)},{id:"shellsession",name:"Shell Session",aliases:["console"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/shellsession.mjs"),[],import.meta.url)},{id:"smalltalk",name:"Smalltalk",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/smalltalk.mjs"),[],import.meta.url)},{id:"solidity",name:"Solidity",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/solidity.mjs"),[],import.meta.url)},{id:"soy",name:"Closure Templates",aliases:["closure-templates"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/soy.mjs"),[],import.meta.url)},{id:"sparql",name:"SPARQL",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/sparql.mjs"),[],import.meta.url)},{id:"splunk",name:"Splunk Query Language",aliases:["spl"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/splunk.mjs"),[],import.meta.url)},{id:"sql",name:"SQL",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/sql.mjs"),[],import.meta.url)},{id:"ssh-config",name:"SSH Config",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/ssh-config.mjs"),[],import.meta.url)},{id:"stata",name:"Stata",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/stata.mjs"),[],import.meta.url)},{id:"stylus",name:"Stylus",aliases:["styl"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/stylus.mjs"),[],import.meta.url)},{id:"svelte",name:"Svelte",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/svelte.mjs"),[],import.meta.url)},{id:"swift",name:"Swift",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/swift.mjs"),[],import.meta.url)},{id:"system-verilog",name:"SystemVerilog",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/system-verilog.mjs"),[],import.meta.url)},{id:"systemd",name:"Systemd Units",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/systemd.mjs"),[],import.meta.url)},{id:"talonscript",name:"TalonScript",aliases:["talon"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/talonscript.mjs"),[],import.meta.url)},{id:"tasl",name:"Tasl",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/tasl.mjs"),[],import.meta.url)},{id:"tcl",name:"Tcl",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/tcl.mjs"),[],import.meta.url)},{id:"templ",name:"Templ",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/templ.mjs"),[],import.meta.url)},{id:"terraform",name:"Terraform",aliases:["tf","tfvars"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/terraform.mjs"),[],import.meta.url)},{id:"tex",name:"TeX",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/tex.mjs"),[],import.meta.url)},{id:"toml",name:"TOML",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/toml.mjs"),[],import.meta.url)},{id:"ts-tags",name:"TypeScript with Tags",aliases:["lit"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/ts-tags.mjs"),[],import.meta.url)},{id:"tsv",name:"TSV",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/tsv.mjs"),[],import.meta.url)},{id:"tsx",name:"TSX",import:()=>u(()=>import("./tsx-B6W0miNI.js"),[],import.meta.url)},{id:"turtle",name:"Turtle",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/turtle.mjs"),[],import.meta.url)},{id:"twig",name:"Twig",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/twig.mjs"),[],import.meta.url)},{id:"typescript",name:"TypeScript",aliases:["ts"],import:()=>u(()=>import("./typescript-Dj6nwHGl.js"),[],import.meta.url)},{id:"typespec",name:"TypeSpec",aliases:["tsp"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/typespec.mjs"),[],import.meta.url)},{id:"typst",name:"Typst",aliases:["typ"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/typst.mjs"),[],import.meta.url)},{id:"v",name:"V",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/v.mjs"),[],import.meta.url)},{id:"vala",name:"Vala",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/vala.mjs"),[],import.meta.url)},{id:"vb",name:"Visual Basic",aliases:["cmd"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/vb.mjs"),[],import.meta.url)},{id:"verilog",name:"Verilog",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/verilog.mjs"),[],import.meta.url)},{id:"vhdl",name:"VHDL",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/vhdl.mjs"),[],import.meta.url)},{id:"viml",name:"Vim Script",aliases:["vim","vimscript"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/viml.mjs"),[],import.meta.url)},{id:"vue",name:"Vue",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/vue.mjs"),[],import.meta.url)},{id:"vue-html",name:"Vue HTML",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/vue-html.mjs"),[],import.meta.url)},{id:"vyper",name:"Vyper",aliases:["vy"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/vyper.mjs"),[],import.meta.url)},{id:"wasm",name:"WebAssembly",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/wasm.mjs"),[],import.meta.url)},{id:"wenyan",name:"Wenyan",aliases:["文言"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/wenyan.mjs"),[],import.meta.url)},{id:"wgsl",name:"WGSL",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/wgsl.mjs"),[],import.meta.url)},{id:"wikitext",name:"Wikitext",aliases:["mediawiki","wiki"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/wikitext.mjs"),[],import.meta.url)},{id:"wolfram",name:"Wolfram",aliases:["wl"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/wolfram.mjs"),[],import.meta.url)},{id:"xml",name:"XML",import:()=>u(()=>import("./xml-GcxqjeRb.js"),[],import.meta.url)},{id:"xsl",name:"XSL",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/xsl.mjs"),[],import.meta.url)},{id:"yaml",name:"YAML",aliases:["yml"],import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/yaml.mjs"),[],import.meta.url)},{id:"zenscript",name:"ZenScript",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/zenscript.mjs"),[],import.meta.url)},{id:"zig",name:"Zig",import:()=>u(()=>import("/Users/<USER>/Desktop/CEP_PROJECT/SahAI/node_modules/@shikijs/langs/dist/zig.mjs"),[],import.meta.url)}],br=Object.fromEntries(Ct.map(r=>[r.id,r.import])),Ar=Object.fromEntries(Ct.flatMap(r=>r.aliases?.map(e=>[e,r.import])||[])),Lr={...br,...Ar},Tr=[{id:"andromeeda",displayName:"Andromeeda",type:"dark",import:()=>u(()=>import("./andromeeda-C3khCPGq.js"),[],import.meta.url)},{id:"aurora-x",displayName:"Aurora X",type:"dark",import:()=>u(()=>import("./aurora-x-D-2ljcwZ.js"),[],import.meta.url)},{id:"ayu-dark",displayName:"Ayu Dark",type:"dark",import:()=>u(()=>import("./ayu-dark-Cv9koXgw.js"),[],import.meta.url)},{id:"catppuccin-frappe",displayName:"Catppuccin Frappé",type:"dark",import:()=>u(()=>import("./catppuccin-frappe-CD_QflpE.js"),[],import.meta.url)},{id:"catppuccin-latte",displayName:"Catppuccin Latte",type:"light",import:()=>u(()=>import("./catppuccin-latte-DRW-0cLl.js"),[],import.meta.url)},{id:"catppuccin-macchiato",displayName:"Catppuccin Macchiato",type:"dark",import:()=>u(()=>import("./catppuccin-macchiato-C-_shW-Y.js"),[],import.meta.url)},{id:"catppuccin-mocha",displayName:"Catppuccin Mocha",type:"dark",import:()=>u(()=>import("./catppuccin-mocha-LGGdnPYs.js"),[],import.meta.url)},{id:"dark-plus",displayName:"Dark Plus",type:"dark",import:()=>u(()=>import("./dark-plus-C3mMm8J8.js"),[],import.meta.url)},{id:"dracula",displayName:"Dracula Theme",type:"dark",import:()=>u(()=>import("./dracula-BzJJZx-M.js"),[],import.meta.url)},{id:"dracula-soft",displayName:"Dracula Theme Soft",type:"dark",import:()=>u(()=>import("./dracula-soft-BXkSAIEj.js"),[],import.meta.url)},{id:"everforest-dark",displayName:"Everforest Dark",type:"dark",import:()=>u(()=>import("./everforest-dark-BgDCqdQA.js"),[],import.meta.url)},{id:"everforest-light",displayName:"Everforest Light",type:"light",import:()=>u(()=>import("./everforest-light-C8M2exoo.js"),[],import.meta.url)},{id:"github-dark",displayName:"GitHub Dark",type:"dark",import:()=>u(()=>import("./github-dark-DHJKELXO.js"),[],import.meta.url)},{id:"github-dark-default",displayName:"GitHub Dark Default",type:"dark",import:()=>u(()=>import("./github-dark-default-Cuk6v7N8.js"),[],import.meta.url)},{id:"github-dark-dimmed",displayName:"GitHub Dark Dimmed",type:"dark",import:()=>u(()=>import("./github-dark-dimmed-DH5Ifo-i.js"),[],import.meta.url)},{id:"github-dark-high-contrast",displayName:"GitHub Dark High Contrast",type:"dark",import:()=>u(()=>import("./github-dark-high-contrast-E3gJ1_iC.js"),[],import.meta.url)},{id:"github-light",displayName:"GitHub Light",type:"light",import:()=>u(()=>import("./github-light-DAi9KRSo.js"),[],import.meta.url)},{id:"github-light-default",displayName:"GitHub Light Default",type:"light",import:()=>u(()=>import("./github-light-default-D7oLnXFd.js"),[],import.meta.url)},{id:"github-light-high-contrast",displayName:"GitHub Light High Contrast",type:"light",import:()=>u(()=>import("./github-light-high-contrast-BfjtVDDH.js"),[],import.meta.url)},{id:"houston",displayName:"Houston",type:"dark",import:()=>u(()=>import("./houston-DnULxvSX.js"),[],import.meta.url)},{id:"kanagawa-dragon",displayName:"Kanagawa Dragon",type:"dark",import:()=>u(()=>import("./kanagawa-dragon-CkXjmgJE.js"),[],import.meta.url)},{id:"kanagawa-lotus",displayName:"Kanagawa Lotus",type:"light",import:()=>u(()=>import("./kanagawa-lotus-CfQXZHmo.js"),[],import.meta.url)},{id:"kanagawa-wave",displayName:"Kanagawa Wave",type:"dark",import:()=>u(()=>import("./kanagawa-wave-DWedfzmr.js"),[],import.meta.url)},{id:"laserwave",displayName:"LaserWave",type:"dark",import:()=>u(()=>import("./laserwave-DUszq2jm.js"),[],import.meta.url)},{id:"light-plus",displayName:"Light Plus",type:"light",import:()=>u(()=>import("./light-plus-B7mTdjB0.js"),[],import.meta.url)},{id:"material-theme",displayName:"Material Theme",type:"dark",import:()=>u(()=>import("./material-theme-D5KoaKCx.js"),[],import.meta.url)},{id:"material-theme-darker",displayName:"Material Theme Darker",type:"dark",import:()=>u(()=>import("./material-theme-darker-BfHTSMKl.js"),[],import.meta.url)},{id:"material-theme-lighter",displayName:"Material Theme Lighter",type:"light",import:()=>u(()=>import("./material-theme-lighter-B0m2ddpp.js"),[],import.meta.url)},{id:"material-theme-ocean",displayName:"Material Theme Ocean",type:"dark",import:()=>u(()=>import("./material-theme-ocean-CyktbL80.js"),[],import.meta.url)},{id:"material-theme-palenight",displayName:"Material Theme Palenight",type:"dark",import:()=>u(()=>import("./material-theme-palenight-Csfq5Kiy.js"),[],import.meta.url)},{id:"min-dark",displayName:"Min Dark",type:"dark",import:()=>u(()=>import("./min-dark-CafNBF8u.js"),[],import.meta.url)},{id:"min-light",displayName:"Min Light",type:"light",import:()=>u(()=>import("./min-light-CTRr51gU.js"),[],import.meta.url)},{id:"monokai",displayName:"Monokai",type:"dark",import:()=>u(()=>import("./monokai-D4h5O-jR.js"),[],import.meta.url)},{id:"night-owl",displayName:"Night Owl",type:"dark",import:()=>u(()=>import("./night-owl-C39BiMTA.js"),[],import.meta.url)},{id:"nord",displayName:"Nord",type:"dark",import:()=>u(()=>import("./nord-Ddv68eIx.js"),[],import.meta.url)},{id:"one-dark-pro",displayName:"One Dark Pro",type:"dark",import:()=>u(()=>import("./one-dark-pro-GBQ2dnAY.js"),[],import.meta.url)},{id:"one-light",displayName:"One Light",type:"light",import:()=>u(()=>import("./one-light-PoHY5YXO.js"),[],import.meta.url)},{id:"plastic",displayName:"Plastic",type:"dark",import:()=>u(()=>import("./plastic-3e1v2bzS.js"),[],import.meta.url)},{id:"poimandres",displayName:"Poimandres",type:"dark",import:()=>u(()=>import("./poimandres-CS3Unz2-.js"),[],import.meta.url)},{id:"red",displayName:"Red",type:"dark",import:()=>u(()=>import("./red-bN70gL4F.js"),[],import.meta.url)},{id:"rose-pine",displayName:"Rosé Pine",type:"dark",import:()=>u(()=>import("./rose-pine-CmCqftbK.js"),[],import.meta.url)},{id:"rose-pine-dawn",displayName:"Rosé Pine Dawn",type:"light",import:()=>u(()=>import("./rose-pine-dawn-Ds-gbosJ.js"),[],import.meta.url)},{id:"rose-pine-moon",displayName:"Rosé Pine Moon",type:"dark",import:()=>u(()=>import("./rose-pine-moon-CjDtw9vr.js"),[],import.meta.url)},{id:"slack-dark",displayName:"Slack Dark",type:"dark",import:()=>u(()=>import("./slack-dark-BthQWCQV.js"),[],import.meta.url)},{id:"slack-ochin",displayName:"Slack Ochin",type:"light",import:()=>u(()=>import("./slack-ochin-DqwNpetd.js"),[],import.meta.url)},{id:"snazzy-light",displayName:"Snazzy Light",type:"light",import:()=>u(()=>import("./snazzy-light-Bw305WKR.js"),[],import.meta.url)},{id:"solarized-dark",displayName:"Solarized Dark",type:"dark",import:()=>u(()=>import("./solarized-dark-DXbdFlpD.js"),[],import.meta.url)},{id:"solarized-light",displayName:"Solarized Light",type:"light",import:()=>u(()=>import("./solarized-light-L9t79GZl.js"),[],import.meta.url)},{id:"synthwave-84",displayName:"Synthwave '84",type:"dark",import:()=>u(()=>import("./synthwave-84-CbfX1IO0.js"),[],import.meta.url)},{id:"tokyo-night",displayName:"Tokyo Night",type:"dark",import:()=>u(()=>import("./tokyo-night-DBQeEorK.js"),[],import.meta.url)},{id:"vesper",displayName:"Vesper",type:"dark",import:()=>u(()=>import("./vesper-BEBZ7ncR.js"),[],import.meta.url)},{id:"vitesse-black",displayName:"Vitesse Black",type:"dark",import:()=>u(()=>import("./vitesse-black-Bkuqu6BP.js"),[],import.meta.url)},{id:"vitesse-dark",displayName:"Vitesse Dark",type:"dark",import:()=>u(()=>import("./vitesse-dark-D0r3Knsf.js"),[],import.meta.url)},{id:"vitesse-light",displayName:"Vitesse Light",type:"light",import:()=>u(()=>import("./vitesse-light-CVO1_9PV.js"),[],import.meta.url)}],Pr=Object.fromEntries(Tr.map(r=>[r.id,r.import]));let W=class extends Error{constructor(e){super(e),this.name="ShikiError"}},Je=class extends Error{constructor(e){super(e),this.name="ShikiError"}};function Sr(){return 2147483648}function Ir(){return typeof performance<"u"?performance.now():Date.now()}const Or=(r,e)=>r+(e-r%e)%e;async function Cr(r){let e,t;const n={};function i(p){t=p,n.HEAPU8=new Uint8Array(p),n.HEAPU32=new Uint32Array(p)}function o(p,f,b){n.HEAPU8.copyWithin(p,f,f+b)}function a(p){try{return e.grow(p-t.byteLength+65535>>>16),i(e.buffer),1}catch{}}function l(p){const f=n.HEAPU8.length;p=p>>>0;const b=Sr();if(p>b)return!1;for(let y=1;y<=4;y*=2){let v=f*(1+.2/y);v=Math.min(v,p+100663296);const E=Math.min(b,Or(Math.max(p,v),65536));if(a(E))return!0}return!1}const s=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0;function c(p,f,b=1024){const y=f+b;let v=f;for(;p[v]&&!(v>=y);)++v;if(v-f>16&&p.buffer&&s)return s.decode(p.subarray(f,v));let E="";for(;f<v;){let R=p[f++];if(!(R&128)){E+=String.fromCharCode(R);continue}const S=p[f++]&63;if((R&224)===192){E+=String.fromCharCode((R&31)<<6|S);continue}const T=p[f++]&63;if((R&240)===224?R=(R&15)<<12|S<<6|T:R=(R&7)<<18|S<<12|T<<6|p[f++]&63,R<65536)E+=String.fromCharCode(R);else{const M=R-65536;E+=String.fromCharCode(55296|M>>10,56320|M&1023)}}return E}function m(p,f){return p?c(n.HEAPU8,p,f):""}const d={emscripten_get_now:Ir,emscripten_memcpy_big:o,emscripten_resize_heap:l,fd_write:()=>0};async function g(){const f=await r({env:d,wasi_snapshot_preview1:d});e=f.memory,i(e.buffer),Object.assign(n,f),n.UTF8ToString=m}return await g(),n}var wr=Object.defineProperty,kr=(r,e,t)=>e in r?wr(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,I=(r,e,t)=>(kr(r,typeof e!="symbol"?e+"":e,t),t);let C=null;function Dr(r){throw new Je(r.UTF8ToString(r.getLastOnigError()))}class Pe{constructor(e){I(this,"utf16Length"),I(this,"utf8Length"),I(this,"utf16Value"),I(this,"utf8Value"),I(this,"utf16OffsetToUtf8"),I(this,"utf8OffsetToUtf16");const t=e.length,n=Pe._utf8ByteLength(e),i=n!==t,o=i?new Uint32Array(t+1):null;i&&(o[t]=n);const a=i?new Uint32Array(n+1):null;i&&(a[n]=t);const l=new Uint8Array(n);let s=0;for(let c=0;c<t;c++){const m=e.charCodeAt(c);let d=m,g=!1;if(m>=55296&&m<=56319&&c+1<t){const p=e.charCodeAt(c+1);p>=56320&&p<=57343&&(d=(m-55296<<10)+65536|p-56320,g=!0)}i&&(o[c]=s,g&&(o[c+1]=s),d<=127?a[s+0]=c:d<=2047?(a[s+0]=c,a[s+1]=c):d<=65535?(a[s+0]=c,a[s+1]=c,a[s+2]=c):(a[s+0]=c,a[s+1]=c,a[s+2]=c,a[s+3]=c)),d<=127?l[s++]=d:d<=2047?(l[s++]=192|(d&1984)>>>6,l[s++]=128|(d&63)>>>0):d<=65535?(l[s++]=224|(d&61440)>>>12,l[s++]=128|(d&4032)>>>6,l[s++]=128|(d&63)>>>0):(l[s++]=240|(d&1835008)>>>18,l[s++]=128|(d&258048)>>>12,l[s++]=128|(d&4032)>>>6,l[s++]=128|(d&63)>>>0),g&&c++}this.utf16Length=t,this.utf8Length=n,this.utf16Value=e,this.utf8Value=l,this.utf16OffsetToUtf8=o,this.utf8OffsetToUtf16=a}static _utf8ByteLength(e){let t=0;for(let n=0,i=e.length;n<i;n++){const o=e.charCodeAt(n);let a=o,l=!1;if(o>=55296&&o<=56319&&n+1<i){const s=e.charCodeAt(n+1);s>=56320&&s<=57343&&(a=(o-55296<<10)+65536|s-56320,l=!0)}a<=127?t+=1:a<=2047?t+=2:a<=65535?t+=3:t+=4,l&&n++}return t}createString(e){const t=e.omalloc(this.utf8Length);return e.HEAPU8.set(this.utf8Value,t),t}}const B=class{constructor(r){if(I(this,"id",++B.LAST_ID),I(this,"_onigBinding"),I(this,"content"),I(this,"utf16Length"),I(this,"utf8Length"),I(this,"utf16OffsetToUtf8"),I(this,"utf8OffsetToUtf16"),I(this,"ptr"),!C)throw new Je("Must invoke loadWasm first.");this._onigBinding=C,this.content=r;const e=new Pe(r);this.utf16Length=e.utf16Length,this.utf8Length=e.utf8Length,this.utf16OffsetToUtf8=e.utf16OffsetToUtf8,this.utf8OffsetToUtf16=e.utf8OffsetToUtf16,this.utf8Length<1e4&&!B._sharedPtrInUse?(B._sharedPtr||(B._sharedPtr=C.omalloc(1e4)),B._sharedPtrInUse=!0,C.HEAPU8.set(e.utf8Value,B._sharedPtr),this.ptr=B._sharedPtr):this.ptr=e.createString(C)}convertUtf8OffsetToUtf16(r){return this.utf8OffsetToUtf16?r<0?0:r>this.utf8Length?this.utf16Length:this.utf8OffsetToUtf16[r]:r}convertUtf16OffsetToUtf8(r){return this.utf16OffsetToUtf8?r<0?0:r>this.utf16Length?this.utf8Length:this.utf16OffsetToUtf8[r]:r}dispose(){this.ptr===B._sharedPtr?B._sharedPtrInUse=!1:this._onigBinding.ofree(this.ptr)}};let se=B;I(se,"LAST_ID",0);I(se,"_sharedPtr",0);I(se,"_sharedPtrInUse",!1);class Nr{constructor(e){if(I(this,"_onigBinding"),I(this,"_ptr"),!C)throw new Je("Must invoke loadWasm first.");const t=[],n=[];for(let l=0,s=e.length;l<s;l++){const c=new Pe(e[l]);t[l]=c.createString(C),n[l]=c.utf8Length}const i=C.omalloc(4*e.length);C.HEAPU32.set(t,i/4);const o=C.omalloc(4*e.length);C.HEAPU32.set(n,o/4);const a=C.createOnigScanner(i,o,e.length);for(let l=0,s=e.length;l<s;l++)C.ofree(t[l]);C.ofree(o),C.ofree(i),a===0&&Dr(C),this._onigBinding=C,this._ptr=a}dispose(){this._onigBinding.freeOnigScanner(this._ptr)}findNextMatchSync(e,t,n){let i=0;if(typeof n=="number"&&(i=n),typeof e=="string"){e=new se(e);const o=this._findNextMatchSync(e,t,!1,i);return e.dispose(),o}return this._findNextMatchSync(e,t,!1,i)}_findNextMatchSync(e,t,n,i){const o=this._onigBinding,a=o.findNextOnigScannerMatch(this._ptr,e.id,e.ptr,e.utf8Length,e.convertUtf16OffsetToUtf8(t),i);if(a===0)return null;const l=o.HEAPU32;let s=a/4;const c=l[s++],m=l[s++],d=[];for(let g=0;g<m;g++){const p=e.convertUtf8OffsetToUtf16(l[s++]),f=e.convertUtf8OffsetToUtf16(l[s++]);d[g]={start:p,end:f,length:f-p}}return{index:c,captureIndices:d}}}function xr(r){return typeof r.instantiator=="function"}function Vr(r){return typeof r.default=="function"}function Mr(r){return typeof r.data<"u"}function Br(r){return typeof Response<"u"&&r instanceof Response}function Gr(r){return typeof ArrayBuffer<"u"&&(r instanceof ArrayBuffer||ArrayBuffer.isView(r))||typeof Buffer<"u"&&Buffer.isBuffer?.(r)||typeof SharedArrayBuffer<"u"&&r instanceof SharedArrayBuffer||typeof Uint32Array<"u"&&r instanceof Uint32Array}let ce;function jr(r){if(ce)return ce;async function e(){C=await Cr(async t=>{let n=r;return n=await n,typeof n=="function"&&(n=await n(t)),typeof n=="function"&&(n=await n(t)),xr(n)?n=await n.instantiator(t):Vr(n)?n=await n.default(t):(Mr(n)&&(n=n.data),Br(n)?typeof WebAssembly.instantiateStreaming=="function"?n=await Ur(n)(t):n=await $r(n)(t):Gr(n)?n=await we(n)(t):n instanceof WebAssembly.Module?n=await we(n)(t):"default"in n&&n.default instanceof WebAssembly.Module&&(n=await we(n.default)(t))),"instance"in n&&(n=n.instance),"exports"in n&&(n=n.exports),n})}return ce=e(),ce}function we(r){return e=>WebAssembly.instantiate(r,e)}function Ur(r){return e=>WebAssembly.instantiateStreaming(r,e)}function $r(r){return async e=>{const t=await r.arrayBuffer();return WebAssembly.instantiate(t,e)}}let Wr;function Hr(){return Wr}async function wt(r){return r&&await jr(r),{createScanner(e){return new Nr(e.map(t=>typeof t=="string"?t:t.source))},createString(e){return new se(e)}}}function Fr(r){return Xe(r)}function Xe(r){return Array.isArray(r)?zr(r):r instanceof RegExp?r:typeof r=="object"?qr(r):r}function zr(r){let e=[];for(let t=0,n=r.length;t<n;t++)e[t]=Xe(r[t]);return e}function qr(r){let e={};for(let t in r)e[t]=Xe(r[t]);return e}function kt(r,...e){return e.forEach(t=>{for(let n in t)r[n]=t[n]}),r}function Dt(r){const e=~r.lastIndexOf("/")||~r.lastIndexOf("\\");return e===0?r:~e===r.length-1?Dt(r.substring(0,r.length-1)):r.substr(~e+1)}var ke=/\$(\d+)|\${(\d+):\/(downcase|upcase)}/g,me=class{static hasCaptures(r){return r===null?!1:(ke.lastIndex=0,ke.test(r))}static replaceCaptures(r,e,t){return r.replace(ke,(n,i,o,a)=>{let l=t[parseInt(i||o,10)];if(l){let s=e.substring(l.start,l.end);for(;s[0]===".";)s=s.substring(1);switch(a){case"downcase":return s.toLowerCase();case"upcase":return s.toUpperCase();default:return s}}else return n})}};function Nt(r,e){return r<e?-1:r>e?1:0}function xt(r,e){if(r===null&&e===null)return 0;if(!r)return-1;if(!e)return 1;let t=r.length,n=e.length;if(t===n){for(let i=0;i<t;i++){let o=Nt(r[i],e[i]);if(o!==0)return o}return 0}return t-n}function lt(r){return!!(/^#[0-9a-f]{6}$/i.test(r)||/^#[0-9a-f]{8}$/i.test(r)||/^#[0-9a-f]{3}$/i.test(r)||/^#[0-9a-f]{4}$/i.test(r))}function Vt(r){return r.replace(/[\-\\\{\}\*\+\?\|\^\$\.\,\[\]\(\)\#\s]/g,"\\$&")}var Mt=class{constructor(r){h(this,"cache",new Map);this.fn=r}get(r){if(this.cache.has(r))return this.cache.get(r);const e=this.fn(r);return this.cache.set(r,e),e}},_e=class{constructor(r,e,t){h(this,"_cachedMatchRoot",new Mt(r=>this._root.match(r)));this._colorMap=r,this._defaults=e,this._root=t}static createFromRawTheme(r,e){return this.createFromParsedTheme(Xr(r),e)}static createFromParsedTheme(r,e){return Qr(r,e)}getColorMap(){return this._colorMap.getColorMap()}getDefaults(){return this._defaults}match(r){if(r===null)return this._defaults;const e=r.scopeName,n=this._cachedMatchRoot.get(e).find(i=>Kr(r.parent,i.parentScopes));return n?new Bt(n.fontStyle,n.foreground,n.background):null}},De=class he{constructor(e,t){this.parent=e,this.scopeName=t}static push(e,t){for(const n of t)e=new he(e,n);return e}static from(...e){let t=null;for(let n=0;n<e.length;n++)t=new he(t,e[n]);return t}push(e){return new he(this,e)}getSegments(){let e=this;const t=[];for(;e;)t.push(e.scopeName),e=e.parent;return t.reverse(),t}toString(){return this.getSegments().join(" ")}extends(e){return this===e?!0:this.parent===null?!1:this.parent.extends(e)}getExtensionIfDefined(e){const t=[];let n=this;for(;n&&n!==e;)t.push(n.scopeName),n=n.parent;return n===e?t.reverse():void 0}};function Kr(r,e){if(e.length===0)return!0;for(let t=0;t<e.length;t++){let n=e[t],i=!1;if(n===">"){if(t===e.length-1)return!1;n=e[++t],i=!0}for(;r&&!Jr(r.scopeName,n);){if(i)return!1;r=r.parent}if(!r)return!1;r=r.parent}return!0}function Jr(r,e){return e===r||r.startsWith(e)&&r[e.length]==="."}var Bt=class{constructor(r,e,t){this.fontStyle=r,this.foregroundId=e,this.backgroundId=t}};function Xr(r){if(!r)return[];if(!r.settings||!Array.isArray(r.settings))return[];let e=r.settings,t=[],n=0;for(let i=0,o=e.length;i<o;i++){let a=e[i];if(!a.settings)continue;let l;if(typeof a.scope=="string"){let d=a.scope;d=d.replace(/^[,]+/,""),d=d.replace(/[,]+$/,""),l=d.split(",")}else Array.isArray(a.scope)?l=a.scope:l=[""];let s=-1;if(typeof a.settings.fontStyle=="string"){s=0;let d=a.settings.fontStyle.split(" ");for(let g=0,p=d.length;g<p;g++)switch(d[g]){case"italic":s=s|1;break;case"bold":s=s|2;break;case"underline":s=s|4;break;case"strikethrough":s=s|8;break}}let c=null;typeof a.settings.foreground=="string"&&lt(a.settings.foreground)&&(c=a.settings.foreground);let m=null;typeof a.settings.background=="string"&&lt(a.settings.background)&&(m=a.settings.background);for(let d=0,g=l.length;d<g;d++){let f=l[d].trim().split(" "),b=f[f.length-1],y=null;f.length>1&&(y=f.slice(0,f.length-1),y.reverse()),t[n++]=new Yr(b,y,i,s,c,m)}}return t}var Yr=class{constructor(r,e,t,n,i,o){this.scope=r,this.parentScopes=e,this.index=t,this.fontStyle=n,this.foreground=i,this.background=o}},$=(r=>(r[r.NotSet=-1]="NotSet",r[r.None=0]="None",r[r.Italic=1]="Italic",r[r.Bold=2]="Bold",r[r.Underline=4]="Underline",r[r.Strikethrough=8]="Strikethrough",r))($||{});function Qr(r,e){r.sort((s,c)=>{let m=Nt(s.scope,c.scope);return m!==0||(m=xt(s.parentScopes,c.parentScopes),m!==0)?m:s.index-c.index});let t=0,n="#000000",i="#ffffff";for(;r.length>=1&&r[0].scope==="";){let s=r.shift();s.fontStyle!==-1&&(t=s.fontStyle),s.foreground!==null&&(n=s.foreground),s.background!==null&&(i=s.background)}let o=new Zr(e),a=new Bt(t,o.getId(n),o.getId(i)),l=new tn(new Ge(0,null,-1,0,0),[]);for(let s=0,c=r.length;s<c;s++){let m=r[s];l.insert(0,m.scope,m.parentScopes,m.fontStyle,o.getId(m.foreground),o.getId(m.background))}return new _e(o,a,l)}var Zr=class{constructor(r){h(this,"_isFrozen");h(this,"_lastColorId");h(this,"_id2color");h(this,"_color2id");if(this._lastColorId=0,this._id2color=[],this._color2id=Object.create(null),Array.isArray(r)){this._isFrozen=!0;for(let e=0,t=r.length;e<t;e++)this._color2id[r[e]]=e,this._id2color[e]=r[e]}else this._isFrozen=!1}getId(r){if(r===null)return 0;r=r.toUpperCase();let e=this._color2id[r];if(e)return e;if(this._isFrozen)throw new Error(`Missing color in color map - ${r}`);return e=++this._lastColorId,this._color2id[r]=e,this._id2color[e]=r,e}getColorMap(){return this._id2color.slice(0)}},en=Object.freeze([]),Ge=class Gt{constructor(e,t,n,i,o){h(this,"scopeDepth");h(this,"parentScopes");h(this,"fontStyle");h(this,"foreground");h(this,"background");this.scopeDepth=e,this.parentScopes=t||en,this.fontStyle=n,this.foreground=i,this.background=o}clone(){return new Gt(this.scopeDepth,this.parentScopes,this.fontStyle,this.foreground,this.background)}static cloneArr(e){let t=[];for(let n=0,i=e.length;n<i;n++)t[n]=e[n].clone();return t}acceptOverwrite(e,t,n,i){this.scopeDepth>e?console.log("how did this happen?"):this.scopeDepth=e,t!==-1&&(this.fontStyle=t),n!==0&&(this.foreground=n),i!==0&&(this.background=i)}},tn=class je{constructor(e,t=[],n={}){h(this,"_rulesWithParentScopes");this._mainRule=e,this._children=n,this._rulesWithParentScopes=t}static _cmpBySpecificity(e,t){if(e.scopeDepth!==t.scopeDepth)return t.scopeDepth-e.scopeDepth;let n=0,i=0;for(;e.parentScopes[n]===">"&&n++,t.parentScopes[i]===">"&&i++,!(n>=e.parentScopes.length||i>=t.parentScopes.length);){const o=t.parentScopes[i].length-e.parentScopes[n].length;if(o!==0)return o;n++,i++}return t.parentScopes.length-e.parentScopes.length}match(e){if(e!==""){let n=e.indexOf("."),i,o;if(n===-1?(i=e,o=""):(i=e.substring(0,n),o=e.substring(n+1)),this._children.hasOwnProperty(i))return this._children[i].match(o)}const t=this._rulesWithParentScopes.concat(this._mainRule);return t.sort(je._cmpBySpecificity),t}insert(e,t,n,i,o,a){if(t===""){this._doInsertHere(e,n,i,o,a);return}let l=t.indexOf("."),s,c;l===-1?(s=t,c=""):(s=t.substring(0,l),c=t.substring(l+1));let m;this._children.hasOwnProperty(s)?m=this._children[s]:(m=new je(this._mainRule.clone(),Ge.cloneArr(this._rulesWithParentScopes)),this._children[s]=m),m.insert(e+1,c,n,i,o,a)}_doInsertHere(e,t,n,i,o){if(t===null){this._mainRule.acceptOverwrite(e,n,i,o);return}for(let a=0,l=this._rulesWithParentScopes.length;a<l;a++){let s=this._rulesWithParentScopes[a];if(xt(s.parentScopes,t)===0){s.acceptOverwrite(e,n,i,o);return}}n===-1&&(n=this._mainRule.fontStyle),i===0&&(i=this._mainRule.foreground),o===0&&(o=this._mainRule.background),this._rulesWithParentScopes.push(new Ge(e,t,n,i,o))}},Y=class x{static toBinaryStr(e){return e.toString(2).padStart(32,"0")}static print(e){const t=x.getLanguageId(e),n=x.getTokenType(e),i=x.getFontStyle(e),o=x.getForeground(e),a=x.getBackground(e);console.log({languageId:t,tokenType:n,fontStyle:i,foreground:o,background:a})}static getLanguageId(e){return(e&255)>>>0}static getTokenType(e){return(e&768)>>>8}static containsBalancedBrackets(e){return(e&1024)!==0}static getFontStyle(e){return(e&30720)>>>11}static getForeground(e){return(e&16744448)>>>15}static getBackground(e){return(e&4278190080)>>>24}static set(e,t,n,i,o,a,l){let s=x.getLanguageId(e),c=x.getTokenType(e),m=x.containsBalancedBrackets(e)?1:0,d=x.getFontStyle(e),g=x.getForeground(e),p=x.getBackground(e);return t!==0&&(s=t),n!==8&&(c=n),i!==null&&(m=i?1:0),o!==-1&&(d=o),a!==0&&(g=a),l!==0&&(p=l),(s<<0|c<<8|m<<10|d<<11|g<<15|p<<24)>>>0}};function ge(r,e){const t=[],n=rn(r);let i=n.next();for(;i!==null;){let s=0;if(i.length===2&&i.charAt(1)===":"){switch(i.charAt(0)){case"R":s=1;break;case"L":s=-1;break;default:console.log(`Unknown priority ${i} in scope selector`)}i=n.next()}let c=a();if(t.push({matcher:c,priority:s}),i!==",")break;i=n.next()}return t;function o(){if(i==="-"){i=n.next();const s=o();return c=>!!s&&!s(c)}if(i==="("){i=n.next();const s=l();return i===")"&&(i=n.next()),s}if(ut(i)){const s=[];do s.push(i),i=n.next();while(ut(i));return c=>e(s,c)}return null}function a(){const s=[];let c=o();for(;c;)s.push(c),c=o();return m=>s.every(d=>d(m))}function l(){const s=[];let c=a();for(;c&&(s.push(c),i==="|"||i===",");){do i=n.next();while(i==="|"||i===",");c=a()}return m=>s.some(d=>d(m))}}function ut(r){return!!r&&!!r.match(/[\w\.:]+/)}function rn(r){let e=/([LR]:|[\w\.:][\w\.:\-]*|[\,\|\-\(\)])/g,t=e.exec(r);return{next:()=>{if(!t)return null;const n=t[0];return t=e.exec(r),n}}}function jt(r){typeof r.dispose=="function"&&r.dispose()}var ne=class{constructor(r){this.scopeName=r}toKey(){return this.scopeName}},nn=class{constructor(r,e){this.scopeName=r,this.ruleName=e}toKey(){return`${this.scopeName}#${this.ruleName}`}},on=class{constructor(){h(this,"_references",[]);h(this,"_seenReferenceKeys",new Set);h(this,"visitedRule",new Set)}get references(){return this._references}add(r){const e=r.toKey();this._seenReferenceKeys.has(e)||(this._seenReferenceKeys.add(e),this._references.push(r))}},an=class{constructor(r,e){h(this,"seenFullScopeRequests",new Set);h(this,"seenPartialScopeRequests",new Set);h(this,"Q");this.repo=r,this.initialScopeName=e,this.seenFullScopeRequests.add(this.initialScopeName),this.Q=[new ne(this.initialScopeName)]}processQueue(){const r=this.Q;this.Q=[];const e=new on;for(const t of r)sn(t,this.initialScopeName,this.repo,e);for(const t of e.references)if(t instanceof ne){if(this.seenFullScopeRequests.has(t.scopeName))continue;this.seenFullScopeRequests.add(t.scopeName),this.Q.push(t)}else{if(this.seenFullScopeRequests.has(t.scopeName)||this.seenPartialScopeRequests.has(t.toKey()))continue;this.seenPartialScopeRequests.add(t.toKey()),this.Q.push(t)}}};function sn(r,e,t,n){const i=t.lookup(r.scopeName);if(!i){if(r.scopeName===e)throw new Error(`No grammar provided for <${e}>`);return}const o=t.lookup(e);r instanceof ne?fe({baseGrammar:o,selfGrammar:i},n):Ue(r.ruleName,{baseGrammar:o,selfGrammar:i,repository:i.repository},n);const a=t.injections(r.scopeName);if(a)for(const l of a)n.add(new ne(l))}function Ue(r,e,t){if(e.repository&&e.repository[r]){const n=e.repository[r];ye([n],e,t)}}function fe(r,e){r.selfGrammar.patterns&&Array.isArray(r.selfGrammar.patterns)&&ye(r.selfGrammar.patterns,{...r,repository:r.selfGrammar.repository},e),r.selfGrammar.injections&&ye(Object.values(r.selfGrammar.injections),{...r,repository:r.selfGrammar.repository},e)}function ye(r,e,t){for(const n of r){if(t.visitedRule.has(n))continue;t.visitedRule.add(n);const i=n.repository?kt({},e.repository,n.repository):e.repository;Array.isArray(n.patterns)&&ye(n.patterns,{...e,repository:i},t);const o=n.include;if(!o)continue;const a=Ut(o);switch(a.kind){case 0:fe({...e,selfGrammar:e.baseGrammar},t);break;case 1:fe(e,t);break;case 2:Ue(a.ruleName,{...e,repository:i},t);break;case 3:case 4:const l=a.scopeName===e.selfGrammar.scopeName?e.selfGrammar:a.scopeName===e.baseGrammar.scopeName?e.baseGrammar:void 0;if(l){const s={baseGrammar:e.baseGrammar,selfGrammar:l,repository:i};a.kind===4?Ue(a.ruleName,s,t):fe(s,t)}else a.kind===4?t.add(new nn(a.scopeName,a.ruleName)):t.add(new ne(a.scopeName));break}}}var ln=class{constructor(){h(this,"kind",0)}},un=class{constructor(){h(this,"kind",1)}},cn=class{constructor(r){h(this,"kind",2);this.ruleName=r}},mn=class{constructor(r){h(this,"kind",3);this.scopeName=r}},pn=class{constructor(r,e){h(this,"kind",4);this.scopeName=r,this.ruleName=e}};function Ut(r){if(r==="$base")return new ln;if(r==="$self")return new un;const e=r.indexOf("#");if(e===-1)return new mn(r);if(e===0)return new cn(r.substring(1));{const t=r.substring(0,e),n=r.substring(e+1);return new pn(t,n)}}var dn=/\\(\d+)/,ct=/\\(\d+)/g,hn=-1,$t=-2;var le=class{constructor(r,e,t,n){h(this,"$location");h(this,"id");h(this,"_nameIsCapturing");h(this,"_name");h(this,"_contentNameIsCapturing");h(this,"_contentName");this.$location=r,this.id=e,this._name=t||null,this._nameIsCapturing=me.hasCaptures(this._name),this._contentName=n||null,this._contentNameIsCapturing=me.hasCaptures(this._contentName)}get debugName(){const r=this.$location?`${Dt(this.$location.filename)}:${this.$location.line}`:"unknown";return`${this.constructor.name}#${this.id} @ ${r}`}getName(r,e){return!this._nameIsCapturing||this._name===null||r===null||e===null?this._name:me.replaceCaptures(this._name,r,e)}getContentName(r,e){return!this._contentNameIsCapturing||this._contentName===null?this._contentName:me.replaceCaptures(this._contentName,r,e)}},fn=class extends le{constructor(e,t,n,i,o){super(e,t,n,i);h(this,"retokenizeCapturedWithRuleId");this.retokenizeCapturedWithRuleId=o}dispose(){}collectPatterns(e,t){throw new Error("Not supported!")}compile(e,t){throw new Error("Not supported!")}compileAG(e,t,n,i){throw new Error("Not supported!")}},_n=class extends le{constructor(e,t,n,i,o){super(e,t,n,null);h(this,"_match");h(this,"captures");h(this,"_cachedCompiledPatterns");this._match=new ie(i,this.id),this.captures=o,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}get debugMatchRegExp(){return`${this._match.source}`}collectPatterns(e,t){t.push(this._match)}compile(e,t){return this._getCachedCompiledPatterns(e).compile(e)}compileAG(e,t,n,i){return this._getCachedCompiledPatterns(e).compileAG(e,n,i)}_getCachedCompiledPatterns(e){return this._cachedCompiledPatterns||(this._cachedCompiledPatterns=new oe,this.collectPatterns(e,this._cachedCompiledPatterns)),this._cachedCompiledPatterns}},mt=class extends le{constructor(e,t,n,i,o){super(e,t,n,i);h(this,"hasMissingPatterns");h(this,"patterns");h(this,"_cachedCompiledPatterns");this.patterns=o.patterns,this.hasMissingPatterns=o.hasMissingPatterns,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}collectPatterns(e,t){for(const n of this.patterns)e.getRule(n).collectPatterns(e,t)}compile(e,t){return this._getCachedCompiledPatterns(e).compile(e)}compileAG(e,t,n,i){return this._getCachedCompiledPatterns(e).compileAG(e,n,i)}_getCachedCompiledPatterns(e){return this._cachedCompiledPatterns||(this._cachedCompiledPatterns=new oe,this.collectPatterns(e,this._cachedCompiledPatterns)),this._cachedCompiledPatterns}},$e=class extends le{constructor(e,t,n,i,o,a,l,s,c,m){super(e,t,n,i);h(this,"_begin");h(this,"beginCaptures");h(this,"_end");h(this,"endHasBackReferences");h(this,"endCaptures");h(this,"applyEndPatternLast");h(this,"hasMissingPatterns");h(this,"patterns");h(this,"_cachedCompiledPatterns");this._begin=new ie(o,this.id),this.beginCaptures=a,this._end=new ie(l||"￿",-1),this.endHasBackReferences=this._end.hasBackReferences,this.endCaptures=s,this.applyEndPatternLast=c||!1,this.patterns=m.patterns,this.hasMissingPatterns=m.hasMissingPatterns,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}get debugBeginRegExp(){return`${this._begin.source}`}get debugEndRegExp(){return`${this._end.source}`}getEndWithResolvedBackReferences(e,t){return this._end.resolveBackReferences(e,t)}collectPatterns(e,t){t.push(this._begin)}compile(e,t){return this._getCachedCompiledPatterns(e,t).compile(e)}compileAG(e,t,n,i){return this._getCachedCompiledPatterns(e,t).compileAG(e,n,i)}_getCachedCompiledPatterns(e,t){if(!this._cachedCompiledPatterns){this._cachedCompiledPatterns=new oe;for(const n of this.patterns)e.getRule(n).collectPatterns(e,this._cachedCompiledPatterns);this.applyEndPatternLast?this._cachedCompiledPatterns.push(this._end.hasBackReferences?this._end.clone():this._end):this._cachedCompiledPatterns.unshift(this._end.hasBackReferences?this._end.clone():this._end)}return this._end.hasBackReferences&&(this.applyEndPatternLast?this._cachedCompiledPatterns.setSource(this._cachedCompiledPatterns.length()-1,t):this._cachedCompiledPatterns.setSource(0,t)),this._cachedCompiledPatterns}},Ee=class extends le{constructor(e,t,n,i,o,a,l,s,c){super(e,t,n,i);h(this,"_begin");h(this,"beginCaptures");h(this,"whileCaptures");h(this,"_while");h(this,"whileHasBackReferences");h(this,"hasMissingPatterns");h(this,"patterns");h(this,"_cachedCompiledPatterns");h(this,"_cachedCompiledWhilePatterns");this._begin=new ie(o,this.id),this.beginCaptures=a,this.whileCaptures=s,this._while=new ie(l,$t),this.whileHasBackReferences=this._while.hasBackReferences,this.patterns=c.patterns,this.hasMissingPatterns=c.hasMissingPatterns,this._cachedCompiledPatterns=null,this._cachedCompiledWhilePatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null),this._cachedCompiledWhilePatterns&&(this._cachedCompiledWhilePatterns.dispose(),this._cachedCompiledWhilePatterns=null)}get debugBeginRegExp(){return`${this._begin.source}`}get debugWhileRegExp(){return`${this._while.source}`}getWhileWithResolvedBackReferences(e,t){return this._while.resolveBackReferences(e,t)}collectPatterns(e,t){t.push(this._begin)}compile(e,t){return this._getCachedCompiledPatterns(e).compile(e)}compileAG(e,t,n,i){return this._getCachedCompiledPatterns(e).compileAG(e,n,i)}_getCachedCompiledPatterns(e){if(!this._cachedCompiledPatterns){this._cachedCompiledPatterns=new oe;for(const t of this.patterns)e.getRule(t).collectPatterns(e,this._cachedCompiledPatterns)}return this._cachedCompiledPatterns}compileWhile(e,t){return this._getCachedCompiledWhilePatterns(e,t).compile(e)}compileWhileAG(e,t,n,i){return this._getCachedCompiledWhilePatterns(e,t).compileAG(e,n,i)}_getCachedCompiledWhilePatterns(e,t){return this._cachedCompiledWhilePatterns||(this._cachedCompiledWhilePatterns=new oe,this._cachedCompiledWhilePatterns.push(this._while.hasBackReferences?this._while.clone():this._while)),this._while.hasBackReferences&&this._cachedCompiledWhilePatterns.setSource(0,t||"￿"),this._cachedCompiledWhilePatterns}},Wt=class w{static createCaptureRule(e,t,n,i,o){return e.registerRule(a=>new fn(t,a,n,i,o))}static getCompiledRuleId(e,t,n){return e.id||t.registerRule(i=>{if(e.id=i,e.match)return new _n(e.$vscodeTextmateLocation,e.id,e.name,e.match,w._compileCaptures(e.captures,t,n));if(typeof e.begin>"u"){e.repository&&(n=kt({},n,e.repository));let o=e.patterns;return typeof o>"u"&&e.include&&(o=[{include:e.include}]),new mt(e.$vscodeTextmateLocation,e.id,e.name,e.contentName,w._compilePatterns(o,t,n))}return e.while?new Ee(e.$vscodeTextmateLocation,e.id,e.name,e.contentName,e.begin,w._compileCaptures(e.beginCaptures||e.captures,t,n),e.while,w._compileCaptures(e.whileCaptures||e.captures,t,n),w._compilePatterns(e.patterns,t,n)):new $e(e.$vscodeTextmateLocation,e.id,e.name,e.contentName,e.begin,w._compileCaptures(e.beginCaptures||e.captures,t,n),e.end,w._compileCaptures(e.endCaptures||e.captures,t,n),e.applyEndPatternLast,w._compilePatterns(e.patterns,t,n))}),e.id}static _compileCaptures(e,t,n){let i=[];if(e){let o=0;for(const a in e){if(a==="$vscodeTextmateLocation")continue;const l=parseInt(a,10);l>o&&(o=l)}for(let a=0;a<=o;a++)i[a]=null;for(const a in e){if(a==="$vscodeTextmateLocation")continue;const l=parseInt(a,10);let s=0;e[a].patterns&&(s=w.getCompiledRuleId(e[a],t,n)),i[l]=w.createCaptureRule(t,e[a].$vscodeTextmateLocation,e[a].name,e[a].contentName,s)}}return i}static _compilePatterns(e,t,n){let i=[];if(e)for(let o=0,a=e.length;o<a;o++){const l=e[o];let s=-1;if(l.include){const c=Ut(l.include);switch(c.kind){case 0:case 1:s=w.getCompiledRuleId(n[l.include],t,n);break;case 2:let m=n[c.ruleName];m&&(s=w.getCompiledRuleId(m,t,n));break;case 3:case 4:const d=c.scopeName,g=c.kind===4?c.ruleName:null,p=t.getExternalGrammar(d,n);if(p)if(g){let f=p.repository[g];f&&(s=w.getCompiledRuleId(f,t,p.repository))}else s=w.getCompiledRuleId(p.repository.$self,t,p.repository);break}}else s=w.getCompiledRuleId(l,t,n);if(s!==-1){const c=t.getRule(s);let m=!1;if((c instanceof mt||c instanceof $e||c instanceof Ee)&&c.hasMissingPatterns&&c.patterns.length===0&&(m=!0),m)continue;i.push(s)}}return{patterns:i,hasMissingPatterns:(e?e.length:0)!==i.length}}},ie=class Ht{constructor(e,t){h(this,"source");h(this,"ruleId");h(this,"hasAnchor");h(this,"hasBackReferences");h(this,"_anchorCache");if(e&&typeof e=="string"){const n=e.length;let i=0,o=[],a=!1;for(let l=0;l<n;l++)if(e.charAt(l)==="\\"&&l+1<n){const c=e.charAt(l+1);c==="z"?(o.push(e.substring(i,l)),o.push("$(?!\\n)(?<!\\n)"),i=l+2):(c==="A"||c==="G")&&(a=!0),l++}this.hasAnchor=a,i===0?this.source=e:(o.push(e.substring(i,n)),this.source=o.join(""))}else this.hasAnchor=!1,this.source=e;this.hasAnchor?this._anchorCache=this._buildAnchorCache():this._anchorCache=null,this.ruleId=t,typeof this.source=="string"?this.hasBackReferences=dn.test(this.source):this.hasBackReferences=!1}clone(){return new Ht(this.source,this.ruleId)}setSource(e){this.source!==e&&(this.source=e,this.hasAnchor&&(this._anchorCache=this._buildAnchorCache()))}resolveBackReferences(e,t){if(typeof this.source!="string")throw new Error("This method should only be called if the source is a string");let n=t.map(i=>e.substring(i.start,i.end));return ct.lastIndex=0,this.source.replace(ct,(i,o)=>Vt(n[parseInt(o,10)]||""))}_buildAnchorCache(){if(typeof this.source!="string")throw new Error("This method should only be called if the source is a string");let e=[],t=[],n=[],i=[],o,a,l,s;for(o=0,a=this.source.length;o<a;o++)l=this.source.charAt(o),e[o]=l,t[o]=l,n[o]=l,i[o]=l,l==="\\"&&o+1<a&&(s=this.source.charAt(o+1),s==="A"?(e[o+1]="￿",t[o+1]="￿",n[o+1]="A",i[o+1]="A"):s==="G"?(e[o+1]="￿",t[o+1]="G",n[o+1]="￿",i[o+1]="G"):(e[o+1]=s,t[o+1]=s,n[o+1]=s,i[o+1]=s),o++);return{A0_G0:e.join(""),A0_G1:t.join(""),A1_G0:n.join(""),A1_G1:i.join("")}}resolveAnchors(e,t){return!this.hasAnchor||!this._anchorCache||typeof this.source!="string"?this.source:e?t?this._anchorCache.A1_G1:this._anchorCache.A1_G0:t?this._anchorCache.A0_G1:this._anchorCache.A0_G0}},oe=class{constructor(){h(this,"_items");h(this,"_hasAnchors");h(this,"_cached");h(this,"_anchorCache");this._items=[],this._hasAnchors=!1,this._cached=null,this._anchorCache={A0_G0:null,A0_G1:null,A1_G0:null,A1_G1:null}}dispose(){this._disposeCaches()}_disposeCaches(){this._cached&&(this._cached.dispose(),this._cached=null),this._anchorCache.A0_G0&&(this._anchorCache.A0_G0.dispose(),this._anchorCache.A0_G0=null),this._anchorCache.A0_G1&&(this._anchorCache.A0_G1.dispose(),this._anchorCache.A0_G1=null),this._anchorCache.A1_G0&&(this._anchorCache.A1_G0.dispose(),this._anchorCache.A1_G0=null),this._anchorCache.A1_G1&&(this._anchorCache.A1_G1.dispose(),this._anchorCache.A1_G1=null)}push(r){this._items.push(r),this._hasAnchors=this._hasAnchors||r.hasAnchor}unshift(r){this._items.unshift(r),this._hasAnchors=this._hasAnchors||r.hasAnchor}length(){return this._items.length}setSource(r,e){this._items[r].source!==e&&(this._disposeCaches(),this._items[r].setSource(e))}compile(r){if(!this._cached){let e=this._items.map(t=>t.source);this._cached=new pt(r,e,this._items.map(t=>t.ruleId))}return this._cached}compileAG(r,e,t){return this._hasAnchors?e?t?(this._anchorCache.A1_G1||(this._anchorCache.A1_G1=this._resolveAnchors(r,e,t)),this._anchorCache.A1_G1):(this._anchorCache.A1_G0||(this._anchorCache.A1_G0=this._resolveAnchors(r,e,t)),this._anchorCache.A1_G0):t?(this._anchorCache.A0_G1||(this._anchorCache.A0_G1=this._resolveAnchors(r,e,t)),this._anchorCache.A0_G1):(this._anchorCache.A0_G0||(this._anchorCache.A0_G0=this._resolveAnchors(r,e,t)),this._anchorCache.A0_G0):this.compile(r)}_resolveAnchors(r,e,t){let n=this._items.map(i=>i.resolveAnchors(e,t));return new pt(r,n,this._items.map(i=>i.ruleId))}},pt=class{constructor(r,e,t){h(this,"scanner");this.regExps=e,this.rules=t,this.scanner=r.createOnigScanner(e)}dispose(){typeof this.scanner.dispose=="function"&&this.scanner.dispose()}toString(){const r=[];for(let e=0,t=this.rules.length;e<t;e++)r.push("   - "+this.rules[e]+": "+this.regExps[e]);return r.join(`
`)}findNextMatchSync(r,e,t){const n=this.scanner.findNextMatchSync(r,e,t);return n?{ruleId:this.rules[n.index],captureIndices:n.captureIndices}:null}},Ne=class{constructor(r,e){this.languageId=r,this.tokenType=e}},U,gn=(U=class{constructor(e,t){h(this,"_defaultAttributes");h(this,"_embeddedLanguagesMatcher");h(this,"_getBasicScopeAttributes",new Mt(e=>{const t=this._scopeToLanguage(e),n=this._toStandardTokenType(e);return new Ne(t,n)}));this._defaultAttributes=new Ne(e,8),this._embeddedLanguagesMatcher=new yn(Object.entries(t||{}))}getDefaultAttributes(){return this._defaultAttributes}getBasicScopeAttributes(e){return e===null?U._NULL_SCOPE_METADATA:this._getBasicScopeAttributes.get(e)}_scopeToLanguage(e){return this._embeddedLanguagesMatcher.match(e)||0}_toStandardTokenType(e){const t=e.match(U.STANDARD_TOKEN_TYPE_REGEXP);if(!t)return 8;switch(t[1]){case"comment":return 1;case"string":return 2;case"regex":return 3;case"meta.embedded":return 0}throw new Error("Unexpected match for standard token type!")}},h(U,"_NULL_SCOPE_METADATA",new Ne(0,0)),h(U,"STANDARD_TOKEN_TYPE_REGEXP",/\b(comment|string|regex|meta\.embedded)\b/),U),yn=class{constructor(r){h(this,"values");h(this,"scopesRegExp");if(r.length===0)this.values=null,this.scopesRegExp=null;else{this.values=new Map(r);const e=r.map(([t,n])=>Vt(t));e.sort(),e.reverse(),this.scopesRegExp=new RegExp(`^((${e.join(")|(")}))($|\\.)`,"")}}match(r){if(!this.scopesRegExp)return;const e=r.match(this.scopesRegExp);if(e)return this.values.get(e[1])}},dt=class{constructor(r,e){this.stack=r,this.stoppedEarly=e}};function Ft(r,e,t,n,i,o,a,l){const s=e.content.length;let c=!1,m=-1;if(a){const p=En(r,e,t,n,i,o);i=p.stack,n=p.linePos,t=p.isFirstLine,m=p.anchorPosition}const d=Date.now();for(;!c;){if(l!==0&&Date.now()-d>l)return new dt(i,!0);g()}return new dt(i,!1);function g(){const p=vn(r,e,t,n,i,m);if(!p){o.produce(i,s),c=!0;return}const f=p.captureIndices,b=p.matchedRuleId,y=f&&f.length>0?f[0].end>n:!1;if(b===hn){const v=i.getRule(r);o.produce(i,f[0].start),i=i.withContentNameScopesList(i.nameScopesList),te(r,e,t,i,o,v.endCaptures,f),o.produce(i,f[0].end);const E=i;if(i=i.parent,m=E.getAnchorPos(),!y&&E.getEnterPos()===n){i=E,o.produce(i,s),c=!0;return}}else{const v=r.getRule(b);o.produce(i,f[0].start);const E=i,R=v.getName(e.content,f),S=i.contentNameScopesList.pushAttributed(R,r);if(i=i.push(b,n,m,f[0].end===s,null,S,S),v instanceof $e){const T=v;te(r,e,t,i,o,T.beginCaptures,f),o.produce(i,f[0].end),m=f[0].end;const M=T.getContentName(e.content,f),H=S.pushAttributed(M,r);if(i=i.withContentNameScopesList(H),T.endHasBackReferences&&(i=i.withEndRule(T.getEndWithResolvedBackReferences(e.content,f))),!y&&E.hasSameRuleAs(i)){i=i.pop(),o.produce(i,s),c=!0;return}}else if(v instanceof Ee){const T=v;te(r,e,t,i,o,T.beginCaptures,f),o.produce(i,f[0].end),m=f[0].end;const M=T.getContentName(e.content,f),H=S.pushAttributed(M,r);if(i=i.withContentNameScopesList(H),T.whileHasBackReferences&&(i=i.withEndRule(T.getWhileWithResolvedBackReferences(e.content,f))),!y&&E.hasSameRuleAs(i)){i=i.pop(),o.produce(i,s),c=!0;return}}else if(te(r,e,t,i,o,v.captures,f),o.produce(i,f[0].end),i=i.pop(),!y){i=i.safePop(),o.produce(i,s),c=!0;return}}f[0].end>n&&(n=f[0].end,t=!1)}}function En(r,e,t,n,i,o){let a=i.beginRuleCapturedEOL?0:-1;const l=[];for(let s=i;s;s=s.pop()){const c=s.getRule(r);c instanceof Ee&&l.push({rule:c,stack:s})}for(let s=l.pop();s;s=l.pop()){const{ruleScanner:c,findOptions:m}=An(s.rule,r,s.stack.endRule,t,n===a),d=c.findNextMatchSync(e,n,m);if(d){if(d.ruleId!==$t){i=s.stack.pop();break}d.captureIndices&&d.captureIndices.length&&(o.produce(s.stack,d.captureIndices[0].start),te(r,e,t,s.stack,o,s.rule.whileCaptures,d.captureIndices),o.produce(s.stack,d.captureIndices[0].end),a=d.captureIndices[0].end,d.captureIndices[0].end>n&&(n=d.captureIndices[0].end,t=!1))}else{i=s.stack.pop();break}}return{stack:i,linePos:n,anchorPosition:a,isFirstLine:t}}function vn(r,e,t,n,i,o){const a=Rn(r,e,t,n,i,o),l=r.getInjections();if(l.length===0)return a;const s=bn(l,r,e,t,n,i,o);if(!s)return a;if(!a)return s;const c=a.captureIndices[0].start,m=s.captureIndices[0].start;return m<c||s.priorityMatch&&m===c?s:a}function Rn(r,e,t,n,i,o){const a=i.getRule(r),{ruleScanner:l,findOptions:s}=zt(a,r,i.endRule,t,n===o),c=l.findNextMatchSync(e,n,s);return c?{captureIndices:c.captureIndices,matchedRuleId:c.ruleId}:null}function bn(r,e,t,n,i,o,a){let l=Number.MAX_VALUE,s=null,c,m=0;const d=o.contentNameScopesList.getScopeNames();for(let g=0,p=r.length;g<p;g++){const f=r[g];if(!f.matcher(d))continue;const b=e.getRule(f.ruleId),{ruleScanner:y,findOptions:v}=zt(b,e,null,n,i===a),E=y.findNextMatchSync(t,i,v);if(!E)continue;const R=E.captureIndices[0].start;if(!(R>=l)&&(l=R,s=E.captureIndices,c=E.ruleId,m=f.priority,l===i))break}return s?{priorityMatch:m===-1,captureIndices:s,matchedRuleId:c}:null}function zt(r,e,t,n,i){return{ruleScanner:r.compileAG(e,t,n,i),findOptions:0}}function An(r,e,t,n,i){return{ruleScanner:r.compileWhileAG(e,t,n,i),findOptions:0}}function te(r,e,t,n,i,o,a){if(o.length===0)return;const l=e.content,s=Math.min(o.length,a.length),c=[],m=a[0].end;for(let d=0;d<s;d++){const g=o[d];if(g===null)continue;const p=a[d];if(p.length===0)continue;if(p.start>m)break;for(;c.length>0&&c[c.length-1].endPos<=p.start;)i.produceFromScopes(c[c.length-1].scopes,c[c.length-1].endPos),c.pop();if(c.length>0?i.produceFromScopes(c[c.length-1].scopes,p.start):i.produce(n,p.start),g.retokenizeCapturedWithRuleId){const b=g.getName(l,a),y=n.contentNameScopesList.pushAttributed(b,r),v=g.getContentName(l,a),E=y.pushAttributed(v,r),R=n.push(g.retokenizeCapturedWithRuleId,p.start,-1,!1,null,y,E),S=r.createOnigString(l.substring(0,p.end));Ft(r,S,t&&p.start===0,p.start,R,i,!1,0),jt(S);continue}const f=g.getName(l,a);if(f!==null){const y=(c.length>0?c[c.length-1].scopes:n.contentNameScopesList).pushAttributed(f,r);c.push(new Ln(y,p.end))}}for(;c.length>0;)i.produceFromScopes(c[c.length-1].scopes,c[c.length-1].endPos),c.pop()}var Ln=class{constructor(r,e){h(this,"scopes");h(this,"endPos");this.scopes=r,this.endPos=e}};function Tn(r,e,t,n,i,o,a,l){return new Sn(r,e,t,n,i,o,a,l)}function ht(r,e,t,n,i){const o=ge(e,ve),a=Wt.getCompiledRuleId(t,n,i.repository);for(const l of o)r.push({debugSelector:e,matcher:l.matcher,ruleId:a,grammar:i,priority:l.priority})}function ve(r,e){if(e.length<r.length)return!1;let t=0;return r.every(n=>{for(let i=t;i<e.length;i++)if(Pn(e[i],n))return t=i+1,!0;return!1})}function Pn(r,e){if(!r)return!1;if(r===e)return!0;const t=e.length;return r.length>t&&r.substr(0,t)===e&&r[t]==="."}var Sn=class{constructor(r,e,t,n,i,o,a,l){h(this,"_rootId");h(this,"_lastRuleId");h(this,"_ruleId2desc");h(this,"_includedGrammars");h(this,"_grammarRepository");h(this,"_grammar");h(this,"_injections");h(this,"_basicScopeAttributesProvider");h(this,"_tokenTypeMatchers");if(this._rootScopeName=r,this.balancedBracketSelectors=o,this._onigLib=l,this._basicScopeAttributesProvider=new gn(t,n),this._rootId=-1,this._lastRuleId=0,this._ruleId2desc=[null],this._includedGrammars={},this._grammarRepository=a,this._grammar=ft(e,null),this._injections=null,this._tokenTypeMatchers=[],i)for(const s of Object.keys(i)){const c=ge(s,ve);for(const m of c)this._tokenTypeMatchers.push({matcher:m.matcher,type:i[s]})}}get themeProvider(){return this._grammarRepository}dispose(){for(const r of this._ruleId2desc)r&&r.dispose()}createOnigScanner(r){return this._onigLib.createOnigScanner(r)}createOnigString(r){return this._onigLib.createOnigString(r)}getMetadataForScope(r){return this._basicScopeAttributesProvider.getBasicScopeAttributes(r)}_collectInjections(){const r={lookup:i=>i===this._rootScopeName?this._grammar:this.getExternalGrammar(i),injections:i=>this._grammarRepository.injections(i)},e=[],t=this._rootScopeName,n=r.lookup(t);if(n){const i=n.injections;if(i)for(let a in i)ht(e,a,i[a],this,n);const o=this._grammarRepository.injections(t);o&&o.forEach(a=>{const l=this.getExternalGrammar(a);if(l){const s=l.injectionSelector;s&&ht(e,s,l,this,l)}})}return e.sort((i,o)=>i.priority-o.priority),e}getInjections(){return this._injections===null&&(this._injections=this._collectInjections()),this._injections}registerRule(r){const e=++this._lastRuleId,t=r(e);return this._ruleId2desc[e]=t,t}getRule(r){return this._ruleId2desc[r]}getExternalGrammar(r,e){if(this._includedGrammars[r])return this._includedGrammars[r];if(this._grammarRepository){const t=this._grammarRepository.lookup(r);if(t)return this._includedGrammars[r]=ft(t,e&&e.$base),this._includedGrammars[r]}}tokenizeLine(r,e,t=0){const n=this._tokenize(r,e,!1,t);return{tokens:n.lineTokens.getResult(n.ruleStack,n.lineLength),ruleStack:n.ruleStack,stoppedEarly:n.stoppedEarly}}tokenizeLine2(r,e,t=0){const n=this._tokenize(r,e,!0,t);return{tokens:n.lineTokens.getBinaryResult(n.ruleStack,n.lineLength),ruleStack:n.ruleStack,stoppedEarly:n.stoppedEarly}}_tokenize(r,e,t,n){this._rootId===-1&&(this._rootId=Wt.getCompiledRuleId(this._grammar.repository.$self,this,this._grammar.repository),this.getInjections());let i;if(!e||e===We.NULL){i=!0;const c=this._basicScopeAttributesProvider.getDefaultAttributes(),m=this.themeProvider.getDefaults(),d=Y.set(0,c.languageId,c.tokenType,null,m.fontStyle,m.foregroundId,m.backgroundId),g=this.getRule(this._rootId).getName(null,null);let p;g?p=re.createRootAndLookUpScopeName(g,d,this):p=re.createRoot("unknown",d),e=new We(null,this._rootId,-1,-1,!1,null,p,p)}else i=!1,e.reset();r=r+`
`;const o=this.createOnigString(r),a=o.content.length,l=new On(t,r,this._tokenTypeMatchers,this.balancedBracketSelectors),s=Ft(this,o,i,0,e,l,!0,n);return jt(o),{lineLength:a,lineTokens:l,ruleStack:s.stack,stoppedEarly:s.stoppedEarly}}};function ft(r,e){return r=Fr(r),r.repository=r.repository||{},r.repository.$self={$vscodeTextmateLocation:r.$vscodeTextmateLocation,patterns:r.patterns,name:r.scopeName},r.repository.$base=e||r.repository.$self,r}var re=class G{constructor(e,t,n){this.parent=e,this.scopePath=t,this.tokenAttributes=n}static fromExtension(e,t){let n=e,i=e?.scopePath??null;for(const o of t)i=De.push(i,o.scopeNames),n=new G(n,i,o.encodedTokenAttributes);return n}static createRoot(e,t){return new G(null,new De(null,e),t)}static createRootAndLookUpScopeName(e,t,n){const i=n.getMetadataForScope(e),o=new De(null,e),a=n.themeProvider.themeMatch(o),l=G.mergeAttributes(t,i,a);return new G(null,o,l)}get scopeName(){return this.scopePath.scopeName}toString(){return this.getScopeNames().join(" ")}equals(e){return G.equals(this,e)}static equals(e,t){do{if(e===t||!e&&!t)return!0;if(!e||!t||e.scopeName!==t.scopeName||e.tokenAttributes!==t.tokenAttributes)return!1;e=e.parent,t=t.parent}while(!0)}static mergeAttributes(e,t,n){let i=-1,o=0,a=0;return n!==null&&(i=n.fontStyle,o=n.foregroundId,a=n.backgroundId),Y.set(e,t.languageId,t.tokenType,null,i,o,a)}pushAttributed(e,t){if(e===null)return this;if(e.indexOf(" ")===-1)return G._pushAttributed(this,e,t);const n=e.split(/ /g);let i=this;for(const o of n)i=G._pushAttributed(i,o,t);return i}static _pushAttributed(e,t,n){const i=n.getMetadataForScope(t),o=e.scopePath.push(t),a=n.themeProvider.themeMatch(o),l=G.mergeAttributes(e.tokenAttributes,i,a);return new G(e,o,l)}getScopeNames(){return this.scopePath.getSegments()}getExtensionIfDefined(e){const t=[];let n=this;for(;n&&n!==e;)t.push({encodedTokenAttributes:n.tokenAttributes,scopeNames:n.scopePath.getExtensionIfDefined(n.parent?.scopePath??null)}),n=n.parent;return n===e?t.reverse():void 0}},V,We=(V=class{constructor(e,t,n,i,o,a,l,s){h(this,"_stackElementBrand");h(this,"_enterPos");h(this,"_anchorPos");h(this,"depth");this.parent=e,this.ruleId=t,this.beginRuleCapturedEOL=o,this.endRule=a,this.nameScopesList=l,this.contentNameScopesList=s,this.depth=this.parent?this.parent.depth+1:1,this._enterPos=n,this._anchorPos=i}equals(e){return e===null?!1:V._equals(this,e)}static _equals(e,t){return e===t?!0:this._structuralEquals(e,t)?re.equals(e.contentNameScopesList,t.contentNameScopesList):!1}static _structuralEquals(e,t){do{if(e===t||!e&&!t)return!0;if(!e||!t||e.depth!==t.depth||e.ruleId!==t.ruleId||e.endRule!==t.endRule)return!1;e=e.parent,t=t.parent}while(!0)}clone(){return this}static _reset(e){for(;e;)e._enterPos=-1,e._anchorPos=-1,e=e.parent}reset(){V._reset(this)}pop(){return this.parent}safePop(){return this.parent?this.parent:this}push(e,t,n,i,o,a,l){return new V(this,e,t,n,i,o,a,l)}getEnterPos(){return this._enterPos}getAnchorPos(){return this._anchorPos}getRule(e){return e.getRule(this.ruleId)}toString(){const e=[];return this._writeString(e,0),"["+e.join(",")+"]"}_writeString(e,t){return this.parent&&(t=this.parent._writeString(e,t)),e[t++]=`(${this.ruleId}, ${this.nameScopesList?.toString()}, ${this.contentNameScopesList?.toString()})`,t}withContentNameScopesList(e){return this.contentNameScopesList===e?this:this.parent.push(this.ruleId,this._enterPos,this._anchorPos,this.beginRuleCapturedEOL,this.endRule,this.nameScopesList,e)}withEndRule(e){return this.endRule===e?this:new V(this.parent,this.ruleId,this._enterPos,this._anchorPos,this.beginRuleCapturedEOL,e,this.nameScopesList,this.contentNameScopesList)}hasSameRuleAs(e){let t=this;for(;t&&t._enterPos===e._enterPos;){if(t.ruleId===e.ruleId)return!0;t=t.parent}return!1}toStateStackFrame(){return{ruleId:this.ruleId,beginRuleCapturedEOL:this.beginRuleCapturedEOL,endRule:this.endRule,nameScopesList:this.nameScopesList?.getExtensionIfDefined(this.parent?.nameScopesList??null)??[],contentNameScopesList:this.contentNameScopesList?.getExtensionIfDefined(this.nameScopesList)??[]}}static pushFrame(e,t){const n=re.fromExtension(e?.nameScopesList??null,t.nameScopesList);return new V(e,t.ruleId,t.enterPos??-1,t.anchorPos??-1,t.beginRuleCapturedEOL,t.endRule,n,re.fromExtension(n,t.contentNameScopesList))}},h(V,"NULL",new V(null,0,0,0,!1,null,null,null)),V),In=class{constructor(r,e){h(this,"balancedBracketScopes");h(this,"unbalancedBracketScopes");h(this,"allowAny",!1);this.balancedBracketScopes=r.flatMap(t=>t==="*"?(this.allowAny=!0,[]):ge(t,ve).map(n=>n.matcher)),this.unbalancedBracketScopes=e.flatMap(t=>ge(t,ve).map(n=>n.matcher))}get matchesAlways(){return this.allowAny&&this.unbalancedBracketScopes.length===0}get matchesNever(){return this.balancedBracketScopes.length===0&&!this.allowAny}match(r){for(const e of this.unbalancedBracketScopes)if(e(r))return!1;for(const e of this.balancedBracketScopes)if(e(r))return!0;return this.allowAny}},On=class{constructor(r,e,t,n){h(this,"_emitBinaryTokens");h(this,"_lineText");h(this,"_tokens");h(this,"_binaryTokens");h(this,"_lastTokenEndIndex");h(this,"_tokenTypeOverrides");this.balancedBracketSelectors=n,this._emitBinaryTokens=r,this._tokenTypeOverrides=t,this._lineText=null,this._tokens=[],this._binaryTokens=[],this._lastTokenEndIndex=0}produce(r,e){this.produceFromScopes(r.contentNameScopesList,e)}produceFromScopes(r,e){if(this._lastTokenEndIndex>=e)return;if(this._emitBinaryTokens){let n=r?.tokenAttributes??0,i=!1;if(this.balancedBracketSelectors?.matchesAlways&&(i=!0),this._tokenTypeOverrides.length>0||this.balancedBracketSelectors&&!this.balancedBracketSelectors.matchesAlways&&!this.balancedBracketSelectors.matchesNever){const o=r?.getScopeNames()??[];for(const a of this._tokenTypeOverrides)a.matcher(o)&&(n=Y.set(n,0,a.type,null,-1,0,0));this.balancedBracketSelectors&&(i=this.balancedBracketSelectors.match(o))}if(i&&(n=Y.set(n,0,8,i,-1,0,0)),this._binaryTokens.length>0&&this._binaryTokens[this._binaryTokens.length-1]===n){this._lastTokenEndIndex=e;return}this._binaryTokens.push(this._lastTokenEndIndex),this._binaryTokens.push(n),this._lastTokenEndIndex=e;return}const t=r?.getScopeNames()??[];this._tokens.push({startIndex:this._lastTokenEndIndex,endIndex:e,scopes:t}),this._lastTokenEndIndex=e}getResult(r,e){return this._tokens.length>0&&this._tokens[this._tokens.length-1].startIndex===e-1&&this._tokens.pop(),this._tokens.length===0&&(this._lastTokenEndIndex=-1,this.produce(r,e),this._tokens[this._tokens.length-1].startIndex=0),this._tokens}getBinaryResult(r,e){this._binaryTokens.length>0&&this._binaryTokens[this._binaryTokens.length-2]===e-1&&(this._binaryTokens.pop(),this._binaryTokens.pop()),this._binaryTokens.length===0&&(this._lastTokenEndIndex=-1,this.produce(r,e),this._binaryTokens[this._binaryTokens.length-2]=0);const t=new Uint32Array(this._binaryTokens.length);for(let n=0,i=this._binaryTokens.length;n<i;n++)t[n]=this._binaryTokens[n];return t}},Cn=class{constructor(r,e){h(this,"_grammars",new Map);h(this,"_rawGrammars",new Map);h(this,"_injectionGrammars",new Map);h(this,"_theme");this._onigLib=e,this._theme=r}dispose(){for(const r of this._grammars.values())r.dispose()}setTheme(r){this._theme=r}getColorMap(){return this._theme.getColorMap()}addGrammar(r,e){this._rawGrammars.set(r.scopeName,r),e&&this._injectionGrammars.set(r.scopeName,e)}lookup(r){return this._rawGrammars.get(r)}injections(r){return this._injectionGrammars.get(r)}getDefaults(){return this._theme.getDefaults()}themeMatch(r){return this._theme.match(r)}grammarForScopeName(r,e,t,n,i){if(!this._grammars.has(r)){let o=this._rawGrammars.get(r);if(!o)return null;this._grammars.set(r,Tn(r,o,e,t,n,i,this,this._onigLib))}return this._grammars.get(r)}},wn=class{constructor(e){h(this,"_options");h(this,"_syncRegistry");h(this,"_ensureGrammarCache");this._options=e,this._syncRegistry=new Cn(_e.createFromRawTheme(e.theme,e.colorMap),e.onigLib),this._ensureGrammarCache=new Map}dispose(){this._syncRegistry.dispose()}setTheme(e,t){this._syncRegistry.setTheme(_e.createFromRawTheme(e,t))}getColorMap(){return this._syncRegistry.getColorMap()}loadGrammarWithEmbeddedLanguages(e,t,n){return this.loadGrammarWithConfiguration(e,t,{embeddedLanguages:n})}loadGrammarWithConfiguration(e,t,n){return this._loadGrammar(e,t,n.embeddedLanguages,n.tokenTypes,new In(n.balancedBracketSelectors||[],n.unbalancedBracketSelectors||[]))}loadGrammar(e){return this._loadGrammar(e,0,null,null,null)}_loadGrammar(e,t,n,i,o){const a=new an(this._syncRegistry,e);for(;a.Q.length>0;)a.Q.map(l=>this._loadSingleGrammar(l.scopeName)),a.processQueue();return this._grammarForScopeName(e,t,n,i,o)}_loadSingleGrammar(e){this._ensureGrammarCache.has(e)||(this._doLoadSingleGrammar(e),this._ensureGrammarCache.set(e,!0))}_doLoadSingleGrammar(e){const t=this._options.loadGrammar(e);if(t){const n=typeof this._options.getInjections=="function"?this._options.getInjections(e):void 0;this._syncRegistry.addGrammar(t,n)}}addGrammar(e,t=[],n=0,i=null){return this._syncRegistry.addGrammar(e,t),this._grammarForScopeName(e.scopeName,n,i)}_grammarForScopeName(e,t=0,n=null,i=null,o=null){return this._syncRegistry.grammarForScopeName(e,t,n,i,o)}},He=We.NULL;const kn=["area","base","basefont","bgsound","br","col","command","embed","frame","hr","image","img","input","keygen","link","meta","param","source","track","wbr"];class ue{constructor(e,t,n){this.normal=t,this.property=e,n&&(this.space=n)}}ue.prototype.normal={};ue.prototype.property={};ue.prototype.space=void 0;function qt(r,e){const t={},n={};for(const i of r)Object.assign(t,i.property),Object.assign(n,i.normal);return new ue(t,n,e)}function Fe(r){return r.toLowerCase()}class D{constructor(e,t){this.attribute=t,this.property=e}}D.prototype.attribute="";D.prototype.booleanish=!1;D.prototype.boolean=!1;D.prototype.commaOrSpaceSeparated=!1;D.prototype.commaSeparated=!1;D.prototype.defined=!1;D.prototype.mustUseProperty=!1;D.prototype.number=!1;D.prototype.overloadedBoolean=!1;D.prototype.property="";D.prototype.spaceSeparated=!1;D.prototype.space=void 0;let Dn=0;const A=q(),P=q(),ze=q(),_=q(),L=q(),J=q(),N=q();function q(){return 2**++Dn}const qe=Object.freeze(Object.defineProperty({__proto__:null,boolean:A,booleanish:P,commaOrSpaceSeparated:N,commaSeparated:J,number:_,overloadedBoolean:ze,spaceSeparated:L},Symbol.toStringTag,{value:"Module"})),xe=Object.keys(qe);class Ye extends D{constructor(e,t,n,i){let o=-1;if(super(e,t),_t(this,"space",i),typeof n=="number")for(;++o<xe.length;){const a=xe[o];_t(this,xe[o],(n&qe[a])===qe[a])}}}Ye.prototype.defined=!0;function _t(r,e,t){t&&(r[e]=t)}function Q(r){const e={},t={};for(const[n,i]of Object.entries(r.properties)){const o=new Ye(n,r.transform(r.attributes||{},n),i,r.space);r.mustUseProperty&&r.mustUseProperty.includes(n)&&(o.mustUseProperty=!0),e[n]=o,t[Fe(n)]=n,t[Fe(o.attribute)]=n}return new ue(e,t,r.space)}const Kt=Q({properties:{ariaActiveDescendant:null,ariaAtomic:P,ariaAutoComplete:null,ariaBusy:P,ariaChecked:P,ariaColCount:_,ariaColIndex:_,ariaColSpan:_,ariaControls:L,ariaCurrent:null,ariaDescribedBy:L,ariaDetails:null,ariaDisabled:P,ariaDropEffect:L,ariaErrorMessage:null,ariaExpanded:P,ariaFlowTo:L,ariaGrabbed:P,ariaHasPopup:null,ariaHidden:P,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:L,ariaLevel:_,ariaLive:null,ariaModal:P,ariaMultiLine:P,ariaMultiSelectable:P,ariaOrientation:null,ariaOwns:L,ariaPlaceholder:null,ariaPosInSet:_,ariaPressed:P,ariaReadOnly:P,ariaRelevant:null,ariaRequired:P,ariaRoleDescription:L,ariaRowCount:_,ariaRowIndex:_,ariaRowSpan:_,ariaSelected:P,ariaSetSize:_,ariaSort:null,ariaValueMax:_,ariaValueMin:_,ariaValueNow:_,ariaValueText:null,role:null},transform(r,e){return e==="role"?e:"aria-"+e.slice(4).toLowerCase()}});function Jt(r,e){return e in r?r[e]:e}function Xt(r,e){return Jt(r,e.toLowerCase())}const Nn=Q({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:J,acceptCharset:L,accessKey:L,action:null,allow:null,allowFullScreen:A,allowPaymentRequest:A,allowUserMedia:A,alt:null,as:null,async:A,autoCapitalize:null,autoComplete:L,autoFocus:A,autoPlay:A,blocking:L,capture:null,charSet:null,checked:A,cite:null,className:L,cols:_,colSpan:null,content:null,contentEditable:P,controls:A,controlsList:L,coords:_|J,crossOrigin:null,data:null,dateTime:null,decoding:null,default:A,defer:A,dir:null,dirName:null,disabled:A,download:ze,draggable:P,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:A,formTarget:null,headers:L,height:_,hidden:ze,high:_,href:null,hrefLang:null,htmlFor:L,httpEquiv:L,id:null,imageSizes:null,imageSrcSet:null,inert:A,inputMode:null,integrity:null,is:null,isMap:A,itemId:null,itemProp:L,itemRef:L,itemScope:A,itemType:L,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:A,low:_,manifest:null,max:null,maxLength:_,media:null,method:null,min:null,minLength:_,multiple:A,muted:A,name:null,nonce:null,noModule:A,noValidate:A,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:A,optimum:_,pattern:null,ping:L,placeholder:null,playsInline:A,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:A,referrerPolicy:null,rel:L,required:A,reversed:A,rows:_,rowSpan:_,sandbox:L,scope:null,scoped:A,seamless:A,selected:A,shadowRootClonable:A,shadowRootDelegatesFocus:A,shadowRootMode:null,shape:null,size:_,sizes:null,slot:null,span:_,spellCheck:P,src:null,srcDoc:null,srcLang:null,srcSet:null,start:_,step:null,style:null,tabIndex:_,target:null,title:null,translate:null,type:null,typeMustMatch:A,useMap:null,value:P,width:_,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:L,axis:null,background:null,bgColor:null,border:_,borderColor:null,bottomMargin:_,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:A,declare:A,event:null,face:null,frame:null,frameBorder:null,hSpace:_,leftMargin:_,link:null,longDesc:null,lowSrc:null,marginHeight:_,marginWidth:_,noResize:A,noHref:A,noShade:A,noWrap:A,object:null,profile:null,prompt:null,rev:null,rightMargin:_,rules:null,scheme:null,scrolling:P,standby:null,summary:null,text:null,topMargin:_,valueType:null,version:null,vAlign:null,vLink:null,vSpace:_,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:A,disableRemotePlayback:A,prefix:null,property:null,results:_,security:null,unselectable:null},space:"html",transform:Xt}),xn=Q({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:N,accentHeight:_,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:_,amplitude:_,arabicForm:null,ascent:_,attributeName:null,attributeType:null,azimuth:_,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:_,by:null,calcMode:null,capHeight:_,className:L,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:_,diffuseConstant:_,direction:null,display:null,dur:null,divisor:_,dominantBaseline:null,download:A,dx:null,dy:null,edgeMode:null,editable:null,elevation:_,enableBackground:null,end:null,event:null,exponent:_,externalResourcesRequired:null,fill:null,fillOpacity:_,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:J,g2:J,glyphName:J,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:_,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:_,horizOriginX:_,horizOriginY:_,id:null,ideographic:_,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:_,k:_,k1:_,k2:_,k3:_,k4:_,kernelMatrix:N,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:_,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:_,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:_,overlineThickness:_,paintOrder:null,panose1:null,path:null,pathLength:_,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:L,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:_,pointsAtY:_,pointsAtZ:_,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:N,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:N,rev:N,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:N,requiredFeatures:N,requiredFonts:N,requiredFormats:N,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:_,specularExponent:_,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:_,strikethroughThickness:_,string:null,stroke:null,strokeDashArray:N,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:_,strokeOpacity:_,strokeWidth:null,style:null,surfaceScale:_,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:N,tabIndex:_,tableValues:null,target:null,targetX:_,targetY:_,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:N,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:_,underlineThickness:_,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:_,values:null,vAlphabetic:_,vMathematical:_,vectorEffect:null,vHanging:_,vIdeographic:_,version:null,vertAdvY:_,vertOriginX:_,vertOriginY:_,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:_,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:Jt}),Yt=Q({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform(r,e){return"xlink:"+e.slice(5).toLowerCase()}}),Qt=Q({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:Xt}),Zt=Q({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform(r,e){return"xml:"+e.slice(3).toLowerCase()}}),Vn=/[A-Z]/g,gt=/-[a-z]/g,Mn=/^data[-\w.:]+$/i;function Bn(r,e){const t=Fe(e);let n=e,i=D;if(t in r.normal)return r.property[r.normal[t]];if(t.length>4&&t.slice(0,4)==="data"&&Mn.test(e)){if(e.charAt(4)==="-"){const o=e.slice(5).replace(gt,jn);n="data"+o.charAt(0).toUpperCase()+o.slice(1)}else{const o=e.slice(4);if(!gt.test(o)){let a=o.replace(Vn,Gn);a.charAt(0)!=="-"&&(a="-"+a),e="data"+a}}i=Ye}return new i(n,e)}function Gn(r){return"-"+r.toLowerCase()}function jn(r){return r.charAt(1).toUpperCase()}const Un=qt([Kt,Nn,Yt,Qt,Zt],"html"),er=qt([Kt,xn,Yt,Qt,Zt],"svg"),yt={}.hasOwnProperty;function $n(r,e){const t=e||{};function n(i,...o){let a=n.invalid;const l=n.handlers;if(i&&yt.call(i,r)){const s=String(i[r]);a=yt.call(l,s)?l[s]:n.unknown}if(a)return a.call(this,i,...o)}return n.handlers=t.handlers||{},n.invalid=t.invalid,n.unknown=t.unknown,n}const Wn=/["&'<>`]/g,Hn=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,Fn=/[\x01-\t\v\f\x0E-\x1F\x7F\x81\x8D\x8F\x90\x9D\xA0-\uFFFF]/g,zn=/[|\\{}()[\]^$+*?.]/g,Et=new WeakMap;function qn(r,e){if(r=r.replace(e.subset?Kn(e.subset):Wn,n),e.subset||e.escapeOnly)return r;return r.replace(Hn,t).replace(Fn,n);function t(i,o,a){return e.format((i.charCodeAt(0)-55296)*1024+i.charCodeAt(1)-56320+65536,a.charCodeAt(o+2),e)}function n(i,o,a){return e.format(i.charCodeAt(0),a.charCodeAt(o+1),e)}}function Kn(r){let e=Et.get(r);return e||(e=Jn(r),Et.set(r,e)),e}function Jn(r){const e=[];let t=-1;for(;++t<r.length;)e.push(r[t].replace(zn,"\\$&"));return new RegExp("(?:"+e.join("|")+")","g")}const Xn=/[\dA-Fa-f]/;function Yn(r,e,t){const n="&#x"+r.toString(16).toUpperCase();return t&&e&&!Xn.test(String.fromCharCode(e))?n:n+";"}const Qn=/\d/;function Zn(r,e,t){const n="&#"+String(r);return t&&e&&!Qn.test(String.fromCharCode(e))?n:n+";"}const ei=["AElig","AMP","Aacute","Acirc","Agrave","Aring","Atilde","Auml","COPY","Ccedil","ETH","Eacute","Ecirc","Egrave","Euml","GT","Iacute","Icirc","Igrave","Iuml","LT","Ntilde","Oacute","Ocirc","Ograve","Oslash","Otilde","Ouml","QUOT","REG","THORN","Uacute","Ucirc","Ugrave","Uuml","Yacute","aacute","acirc","acute","aelig","agrave","amp","aring","atilde","auml","brvbar","ccedil","cedil","cent","copy","curren","deg","divide","eacute","ecirc","egrave","eth","euml","frac12","frac14","frac34","gt","iacute","icirc","iexcl","igrave","iquest","iuml","laquo","lt","macr","micro","middot","nbsp","not","ntilde","oacute","ocirc","ograve","ordf","ordm","oslash","otilde","ouml","para","plusmn","pound","quot","raquo","reg","sect","shy","sup1","sup2","sup3","szlig","thorn","times","uacute","ucirc","ugrave","uml","uuml","yacute","yen","yuml"],Ve={nbsp:" ",iexcl:"¡",cent:"¢",pound:"£",curren:"¤",yen:"¥",brvbar:"¦",sect:"§",uml:"¨",copy:"©",ordf:"ª",laquo:"«",not:"¬",shy:"­",reg:"®",macr:"¯",deg:"°",plusmn:"±",sup2:"²",sup3:"³",acute:"´",micro:"µ",para:"¶",middot:"·",cedil:"¸",sup1:"¹",ordm:"º",raquo:"»",frac14:"¼",frac12:"½",frac34:"¾",iquest:"¿",Agrave:"À",Aacute:"Á",Acirc:"Â",Atilde:"Ã",Auml:"Ä",Aring:"Å",AElig:"Æ",Ccedil:"Ç",Egrave:"È",Eacute:"É",Ecirc:"Ê",Euml:"Ë",Igrave:"Ì",Iacute:"Í",Icirc:"Î",Iuml:"Ï",ETH:"Ð",Ntilde:"Ñ",Ograve:"Ò",Oacute:"Ó",Ocirc:"Ô",Otilde:"Õ",Ouml:"Ö",times:"×",Oslash:"Ø",Ugrave:"Ù",Uacute:"Ú",Ucirc:"Û",Uuml:"Ü",Yacute:"Ý",THORN:"Þ",szlig:"ß",agrave:"à",aacute:"á",acirc:"â",atilde:"ã",auml:"ä",aring:"å",aelig:"æ",ccedil:"ç",egrave:"è",eacute:"é",ecirc:"ê",euml:"ë",igrave:"ì",iacute:"í",icirc:"î",iuml:"ï",eth:"ð",ntilde:"ñ",ograve:"ò",oacute:"ó",ocirc:"ô",otilde:"õ",ouml:"ö",divide:"÷",oslash:"ø",ugrave:"ù",uacute:"ú",ucirc:"û",uuml:"ü",yacute:"ý",thorn:"þ",yuml:"ÿ",fnof:"ƒ",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",bull:"•",hellip:"…",prime:"′",Prime:"″",oline:"‾",frasl:"⁄",weierp:"℘",image:"ℑ",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",lang:"〈",rang:"〉",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦",quot:'"',amp:"&",lt:"<",gt:">",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",circ:"ˆ",tilde:"˜",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",permil:"‰",lsaquo:"‹",rsaquo:"›",euro:"€"},ti=["cent","copy","divide","gt","lt","not","para","times"],tr={}.hasOwnProperty,Ke={};let pe;for(pe in Ve)tr.call(Ve,pe)&&(Ke[Ve[pe]]=pe);const ri=/[^\dA-Za-z]/;function ni(r,e,t,n){const i=String.fromCharCode(r);if(tr.call(Ke,i)){const o=Ke[i],a="&"+o;return t&&ei.includes(o)&&!ti.includes(o)&&(!n||e&&e!==61&&ri.test(String.fromCharCode(e)))?a:a+";"}return""}function ii(r,e,t){let n=Yn(r,e,t.omitOptionalSemicolons),i;if((t.useNamedReferences||t.useShortestReferences)&&(i=ni(r,e,t.omitOptionalSemicolons,t.attribute)),(t.useShortestReferences||!i)&&t.useShortestReferences){const o=Zn(r,e,t.omitOptionalSemicolons);o.length<n.length&&(n=o)}return i&&(!t.useShortestReferences||i.length<n.length)?i:n}function X(r,e){return qn(r,Object.assign({format:ii},e))}const oi=/^>|^->|<!--|-->|--!>|<!-$/g,ai=[">"],si=["<",">"];function li(r,e,t,n){return n.settings.bogusComments?"<?"+X(r.value,Object.assign({},n.settings.characterReferences,{subset:ai}))+">":"<!--"+r.value.replace(oi,i)+"-->";function i(o){return X(o,Object.assign({},n.settings.characterReferences,{subset:si}))}}function ui(r,e,t,n){return"<!"+(n.settings.upperDoctype?"DOCTYPE":"doctype")+(n.settings.tightDoctype?"":" ")+"html>"}function vt(r,e){const t=String(r);if(typeof e!="string")throw new TypeError("Expected character");let n=0,i=t.indexOf(e);for(;i!==-1;)n++,i=t.indexOf(e,i+e.length);return n}function ci(r,e){const t=e||{};return(r[r.length-1]===""?[...r,""]:r).join((t.padRight?" ":"")+","+(t.padLeft===!1?"":" ")).trim()}function mi(r){return r.join(" ").trim()}const pi=/[ \t\n\f\r]/g;function Qe(r){return typeof r=="object"?r.type==="text"?Rt(r.value):!1:Rt(r)}function Rt(r){return r.replace(pi,"")===""}const O=nr(1),rr=nr(-1),di=[];function nr(r){return e;function e(t,n,i){const o=t?t.children:di;let a=(n||0)+r,l=o[a];if(!i)for(;l&&Qe(l);)a+=r,l=o[a];return l}}const hi={}.hasOwnProperty;function ir(r){return e;function e(t,n,i){return hi.call(r,t.tagName)&&r[t.tagName](t,n,i)}}const Ze=ir({body:_i,caption:Me,colgroup:Me,dd:vi,dt:Ei,head:Me,html:fi,li:yi,optgroup:Ri,option:bi,p:gi,rp:bt,rt:bt,tbody:Li,td:At,tfoot:Ti,th:At,thead:Ai,tr:Pi});function Me(r,e,t){const n=O(t,e,!0);return!n||n.type!=="comment"&&!(n.type==="text"&&Qe(n.value.charAt(0)))}function fi(r,e,t){const n=O(t,e);return!n||n.type!=="comment"}function _i(r,e,t){const n=O(t,e);return!n||n.type!=="comment"}function gi(r,e,t){const n=O(t,e);return n?n.type==="element"&&(n.tagName==="address"||n.tagName==="article"||n.tagName==="aside"||n.tagName==="blockquote"||n.tagName==="details"||n.tagName==="div"||n.tagName==="dl"||n.tagName==="fieldset"||n.tagName==="figcaption"||n.tagName==="figure"||n.tagName==="footer"||n.tagName==="form"||n.tagName==="h1"||n.tagName==="h2"||n.tagName==="h3"||n.tagName==="h4"||n.tagName==="h5"||n.tagName==="h6"||n.tagName==="header"||n.tagName==="hgroup"||n.tagName==="hr"||n.tagName==="main"||n.tagName==="menu"||n.tagName==="nav"||n.tagName==="ol"||n.tagName==="p"||n.tagName==="pre"||n.tagName==="section"||n.tagName==="table"||n.tagName==="ul"):!t||!(t.type==="element"&&(t.tagName==="a"||t.tagName==="audio"||t.tagName==="del"||t.tagName==="ins"||t.tagName==="map"||t.tagName==="noscript"||t.tagName==="video"))}function yi(r,e,t){const n=O(t,e);return!n||n.type==="element"&&n.tagName==="li"}function Ei(r,e,t){const n=O(t,e);return!!(n&&n.type==="element"&&(n.tagName==="dt"||n.tagName==="dd"))}function vi(r,e,t){const n=O(t,e);return!n||n.type==="element"&&(n.tagName==="dt"||n.tagName==="dd")}function bt(r,e,t){const n=O(t,e);return!n||n.type==="element"&&(n.tagName==="rp"||n.tagName==="rt")}function Ri(r,e,t){const n=O(t,e);return!n||n.type==="element"&&n.tagName==="optgroup"}function bi(r,e,t){const n=O(t,e);return!n||n.type==="element"&&(n.tagName==="option"||n.tagName==="optgroup")}function Ai(r,e,t){const n=O(t,e);return!!(n&&n.type==="element"&&(n.tagName==="tbody"||n.tagName==="tfoot"))}function Li(r,e,t){const n=O(t,e);return!n||n.type==="element"&&(n.tagName==="tbody"||n.tagName==="tfoot")}function Ti(r,e,t){return!O(t,e)}function Pi(r,e,t){const n=O(t,e);return!n||n.type==="element"&&n.tagName==="tr"}function At(r,e,t){const n=O(t,e);return!n||n.type==="element"&&(n.tagName==="td"||n.tagName==="th")}const Si=ir({body:Ci,colgroup:wi,head:Oi,html:Ii,tbody:ki});function Ii(r){const e=O(r,-1);return!e||e.type!=="comment"}function Oi(r){const e=new Set;for(const n of r.children)if(n.type==="element"&&(n.tagName==="base"||n.tagName==="title")){if(e.has(n.tagName))return!1;e.add(n.tagName)}const t=r.children[0];return!t||t.type==="element"}function Ci(r){const e=O(r,-1,!0);return!e||e.type!=="comment"&&!(e.type==="text"&&Qe(e.value.charAt(0)))&&!(e.type==="element"&&(e.tagName==="meta"||e.tagName==="link"||e.tagName==="script"||e.tagName==="style"||e.tagName==="template"))}function wi(r,e,t){const n=rr(t,e),i=O(r,-1,!0);return t&&n&&n.type==="element"&&n.tagName==="colgroup"&&Ze(n,t.children.indexOf(n),t)?!1:!!(i&&i.type==="element"&&i.tagName==="col")}function ki(r,e,t){const n=rr(t,e),i=O(r,-1);return t&&n&&n.type==="element"&&(n.tagName==="thead"||n.tagName==="tbody")&&Ze(n,t.children.indexOf(n),t)?!1:!!(i&&i.type==="element"&&i.tagName==="tr")}const de={name:[[`	
\f\r &/=>`.split(""),`	
\f\r "&'/=>\``.split("")],[`\0	
\f\r "&'/<=>`.split(""),`\0	
\f\r "&'/<=>\``.split("")]],unquoted:[[`	
\f\r &>`.split(""),`\0	
\f\r "&'<=>\``.split("")],[`\0	
\f\r "&'<=>\``.split(""),`\0	
\f\r "&'<=>\``.split("")]],single:[["&'".split(""),"\"&'`".split("")],["\0&'".split(""),"\0\"&'`".split("")]],double:[['"&'.split(""),"\"&'`".split("")],['\0"&'.split(""),"\0\"&'`".split("")]]};function Di(r,e,t,n){const i=n.schema,o=i.space==="svg"?!1:n.settings.omitOptionalTags;let a=i.space==="svg"?n.settings.closeEmptyElements:n.settings.voids.includes(r.tagName.toLowerCase());const l=[];let s;i.space==="html"&&r.tagName==="svg"&&(n.schema=er);const c=Ni(n,r.properties),m=n.all(i.space==="html"&&r.tagName==="template"?r.content:r);return n.schema=i,m&&(a=!1),(c||!o||!Si(r,e,t))&&(l.push("<",r.tagName,c?" "+c:""),a&&(i.space==="svg"||n.settings.closeSelfClosing)&&(s=c.charAt(c.length-1),(!n.settings.tightSelfClosing||s==="/"||s&&s!=='"'&&s!=="'")&&l.push(" "),l.push("/")),l.push(">")),l.push(m),!a&&(!o||!Ze(r,e,t))&&l.push("</"+r.tagName+">"),l.join("")}function Ni(r,e){const t=[];let n=-1,i;if(e){for(i in e)if(e[i]!==null&&e[i]!==void 0){const o=xi(r,i,e[i]);o&&t.push(o)}}for(;++n<t.length;){const o=r.settings.tightAttributes?t[n].charAt(t[n].length-1):void 0;n!==t.length-1&&o!=='"'&&o!=="'"&&(t[n]+=" ")}return t.join("")}function xi(r,e,t){const n=Bn(r.schema,e),i=r.settings.allowParseErrors&&r.schema.space==="html"?0:1,o=r.settings.allowDangerousCharacters?0:1;let a=r.quote,l;if(n.overloadedBoolean&&(t===n.attribute||t==="")?t=!0:(n.boolean||n.overloadedBoolean)&&(typeof t!="string"||t===n.attribute||t==="")&&(t=!!t),t==null||t===!1||typeof t=="number"&&Number.isNaN(t))return"";const s=X(n.attribute,Object.assign({},r.settings.characterReferences,{subset:de.name[i][o]}));return t===!0||(t=Array.isArray(t)?(n.commaSeparated?ci:mi)(t,{padLeft:!r.settings.tightCommaSeparatedLists}):String(t),r.settings.collapseEmptyAttributes&&!t)?s:(r.settings.preferUnquoted&&(l=X(t,Object.assign({},r.settings.characterReferences,{attribute:!0,subset:de.unquoted[i][o]}))),l!==t&&(r.settings.quoteSmart&&vt(t,a)>vt(t,r.alternative)&&(a=r.alternative),l=a+X(t,Object.assign({},r.settings.characterReferences,{subset:(a==="'"?de.single:de.double)[i][o],attribute:!0}))+a),s+(l&&"="+l))}const Vi=["<","&"];function or(r,e,t,n){return t&&t.type==="element"&&(t.tagName==="script"||t.tagName==="style")?r.value:X(r.value,Object.assign({},n.settings.characterReferences,{subset:Vi}))}function Mi(r,e,t,n){return n.settings.allowDangerousHtml?r.value:or(r,e,t,n)}function Bi(r,e,t,n){return n.all(r)}const Gi=$n("type",{invalid:ji,unknown:Ui,handlers:{comment:li,doctype:ui,element:Di,raw:Mi,root:Bi,text:or}});function ji(r){throw new Error("Expected node, not `"+r+"`")}function Ui(r){const e=r;throw new Error("Cannot compile unknown node `"+e.type+"`")}const $i={},Wi={},Hi=[];function Fi(r,e){const t=$i,n=t.quote||'"',i=n==='"'?"'":'"';if(n!=='"'&&n!=="'")throw new Error("Invalid quote `"+n+"`, expected `'` or `\"`");return{one:zi,all:qi,settings:{omitOptionalTags:t.omitOptionalTags||!1,allowParseErrors:t.allowParseErrors||!1,allowDangerousCharacters:t.allowDangerousCharacters||!1,quoteSmart:t.quoteSmart||!1,preferUnquoted:t.preferUnquoted||!1,tightAttributes:t.tightAttributes||!1,upperDoctype:t.upperDoctype||!1,tightDoctype:t.tightDoctype||!1,bogusComments:t.bogusComments||!1,tightCommaSeparatedLists:t.tightCommaSeparatedLists||!1,tightSelfClosing:t.tightSelfClosing||!1,collapseEmptyAttributes:t.collapseEmptyAttributes||!1,allowDangerousHtml:t.allowDangerousHtml||!1,voids:t.voids||kn,characterReferences:t.characterReferences||Wi,closeSelfClosing:t.closeSelfClosing||!1,closeEmptyElements:t.closeEmptyElements||!1},schema:t.space==="svg"?er:Un,quote:n,alternative:i}.one(Array.isArray(r)?{type:"root",children:r}:r,void 0,void 0)}function zi(r,e,t){return Gi(r,e,t,this)}function qi(r){const e=[],t=r&&r.children||Hi;let n=-1;for(;++n<t.length;)e[n]=this.one(t[n],n,r);return e.join("")}function Ki(r){return Array.isArray(r)?r:[r]}function Se(r,e=!1){const t=r.split(/(\r?\n)/g);let n=0;const i=[];for(let o=0;o<t.length;o+=2){const a=e?t[o]+(t[o+1]||""):t[o];i.push([a,n]),n+=t[o].length,n+=t[o+1]?.length||0}return i}function et(r){return!r||["plaintext","txt","text","plain"].includes(r)}function ar(r){return r==="ansi"||et(r)}function tt(r){return r==="none"}function sr(r){return tt(r)}function lr(r,e){var n;if(!e)return r;r.properties||(r.properties={}),(n=r.properties).class||(n.class=[]),typeof r.properties.class=="string"&&(r.properties.class=r.properties.class.split(/\s+/g)),Array.isArray(r.properties.class)||(r.properties.class=[]);const t=Array.isArray(e)?e:e.split(/\s+/g);for(const i of t)i&&!r.properties.class.includes(i)&&r.properties.class.push(i);return r}function Ji(r,e){let t=0;const n=[];for(const i of e)i>t&&n.push({...r,content:r.content.slice(t,i),offset:r.offset+t}),t=i;return t<r.content.length&&n.push({...r,content:r.content.slice(t),offset:r.offset+t}),n}function Xi(r,e){const t=Array.from(e instanceof Set?e:new Set(e)).sort((n,i)=>n-i);return t.length?r.map(n=>n.flatMap(i=>{const o=t.filter(a=>i.offset<a&&a<i.offset+i.content.length).map(a=>a-i.offset).sort((a,l)=>a-l);return o.length?Ji(i,o):i})):r}async function ur(r){return Promise.resolve(typeof r=="function"?r():r).then(e=>e.default||e)}function Re(r,e){const t=typeof r=="string"?{}:{...r.colorReplacements},n=typeof r=="string"?r:r.name;for(const[i,o]of Object.entries(e?.colorReplacements||{}))typeof o=="string"?t[i]=o:i===n&&Object.assign(t,o);return t}function F(r,e){return r&&(e?.[r?.toLowerCase()]||r)}function cr(r){const e={};return r.color&&(e.color=r.color),r.bgColor&&(e["background-color"]=r.bgColor),r.fontStyle&&(r.fontStyle&$.Italic&&(e["font-style"]="italic"),r.fontStyle&$.Bold&&(e["font-weight"]="bold"),r.fontStyle&$.Underline&&(e["text-decoration"]="underline")),e}function Yi(r){return typeof r=="string"?r:Object.entries(r).map(([e,t])=>`${e}:${t}`).join(";")}function Qi(r){const e=Se(r,!0).map(([i])=>i);function t(i){if(i===r.length)return{line:e.length-1,character:e[e.length-1].length};let o=i,a=0;for(const l of e){if(o<l.length)break;o-=l.length,a++}return{line:a,character:o}}function n(i,o){let a=0;for(let l=0;l<i;l++)a+=e[l].length;return a+=o,a}return{lines:e,indexToPos:t,posToIndex:n}}class k extends Error{constructor(e){super(e),this.name="ShikiError"}}const mr=new WeakMap;function Ie(r,e){mr.set(r,e)}function ae(r){return mr.get(r)}class Z{constructor(...e){h(this,"_stacks",{});h(this,"lang");if(e.length===2){const[t,n]=e;this.lang=n,this._stacks=t}else{const[t,n,i]=e;this.lang=n,this._stacks={[i]:t}}}get themes(){return Object.keys(this._stacks)}get theme(){return this.themes[0]}get _stack(){return this._stacks[this.theme]}static initial(e,t){return new Z(Object.fromEntries(Ki(t).map(n=>[n,He])),e)}getInternalStack(e=this.theme){return this._stacks[e]}get scopes(){return Lt(this._stacks[this.theme])}getScopes(e=this.theme){return Lt(this._stacks[e])}toJSON(){return{lang:this.lang,theme:this.theme,themes:this.themes,scopes:this.scopes}}}function Lt(r){const e=[],t=new Set;function n(i){if(t.has(i))return;t.add(i);const o=i?.nameScopesList?.scopeName;o&&e.push(o),i.parent&&n(i.parent)}return n(r),e}function Zi(r,e){if(!(r instanceof Z))throw new k("Invalid grammar state");return r.getInternalStack(e)}function eo(){const r=new WeakMap;function e(t){if(!r.has(t.meta)){let n=function(a){if(typeof a=="number"){if(a<0||a>t.source.length)throw new k(`Invalid decoration offset: ${a}. Code length: ${t.source.length}`);return{...i.indexToPos(a),offset:a}}else{const l=i.lines[a.line];if(l===void 0)throw new k(`Invalid decoration position ${JSON.stringify(a)}. Lines length: ${i.lines.length}`);if(a.character<0||a.character>l.length)throw new k(`Invalid decoration position ${JSON.stringify(a)}. Line ${a.line} length: ${l.length}`);return{...a,offset:i.posToIndex(a.line,a.character)}}};const i=Qi(t.source),o=(t.options.decorations||[]).map(a=>({...a,start:n(a.start),end:n(a.end)}));to(o),r.set(t.meta,{decorations:o,converter:i,source:t.source})}return r.get(t.meta)}return{name:"shiki:decorations",tokens(t){if(!this.options.decorations?.length)return;const i=e(this).decorations.flatMap(a=>[a.start.offset,a.end.offset]);return Xi(t,i)},code(t){if(!this.options.decorations?.length)return;const n=e(this),i=Array.from(t.children).filter(m=>m.type==="element"&&m.tagName==="span");if(i.length!==n.converter.lines.length)throw new k(`Number of lines in code element (${i.length}) does not match the number of lines in the source (${n.converter.lines.length}). Failed to apply decorations.`);function o(m,d,g,p){const f=i[m];let b="",y=-1,v=-1;if(d===0&&(y=0),g===0&&(v=0),g===Number.POSITIVE_INFINITY&&(v=f.children.length),y===-1||v===-1)for(let R=0;R<f.children.length;R++)b+=pr(f.children[R]),y===-1&&b.length===d&&(y=R+1),v===-1&&b.length===g&&(v=R+1);if(y===-1)throw new k(`Failed to find start index for decoration ${JSON.stringify(p.start)}`);if(v===-1)throw new k(`Failed to find end index for decoration ${JSON.stringify(p.end)}`);const E=f.children.slice(y,v);if(!p.alwaysWrap&&E.length===f.children.length)l(f,p,"line");else if(!p.alwaysWrap&&E.length===1&&E[0].type==="element")l(E[0],p,"token");else{const R={type:"element",tagName:"span",properties:{},children:E};l(R,p,"wrapper"),f.children.splice(y,E.length,R)}}function a(m,d){i[m]=l(i[m],d,"line")}function l(m,d,g){const p=d.properties||{},f=d.transform||(b=>b);return m.tagName=d.tagName||"span",m.properties={...m.properties,...p,class:m.properties.class},d.properties?.class&&lr(m,d.properties.class),m=f(m,g)||m,m}const s=[],c=n.decorations.sort((m,d)=>d.start.offset-m.start.offset);for(const m of c){const{start:d,end:g}=m;if(d.line===g.line)o(d.line,d.character,g.character,m);else if(d.line<g.line){o(d.line,d.character,Number.POSITIVE_INFINITY,m);for(let p=d.line+1;p<g.line;p++)s.unshift(()=>a(p,m));o(g.line,0,g.character,m)}}s.forEach(m=>m())}}}function to(r){for(let e=0;e<r.length;e++){const t=r[e];if(t.start.offset>t.end.offset)throw new k(`Invalid decoration range: ${JSON.stringify(t.start)} - ${JSON.stringify(t.end)}`);for(let n=e+1;n<r.length;n++){const i=r[n],o=t.start.offset<i.start.offset&&i.start.offset<t.end.offset,a=t.start.offset<i.end.offset&&i.end.offset<t.end.offset,l=i.start.offset<t.start.offset&&t.start.offset<i.end.offset,s=i.start.offset<t.end.offset&&t.end.offset<i.end.offset;if(o||a||l||s){if(a&&a||l&&s)continue;throw new k(`Decorations ${JSON.stringify(t.start)} and ${JSON.stringify(i.start)} intersect.`)}}}}function pr(r){return r.type==="text"?r.value:r.type==="element"?r.children.map(pr).join(""):""}const ro=[eo()];function be(r){return[...r.transformers||[],...ro]}var z=["black","red","green","yellow","blue","magenta","cyan","white","brightBlack","brightRed","brightGreen","brightYellow","brightBlue","brightMagenta","brightCyan","brightWhite"],Be={1:"bold",2:"dim",3:"italic",4:"underline",7:"reverse",9:"strikethrough"};function no(r,e){const t=r.indexOf("\x1B[",e);if(t!==-1){const n=r.indexOf("m",t);return{sequence:r.substring(t+2,n).split(";"),startPosition:t,position:n+1}}return{position:r.length}}function Tt(r,e){let t=1;const n=r[e+t++];let i;if(n==="2"){const o=[r[e+t++],r[e+t++],r[e+t]].map(a=>Number.parseInt(a));o.length===3&&!o.some(a=>Number.isNaN(a))&&(i={type:"rgb",rgb:o})}else if(n==="5"){const o=Number.parseInt(r[e+t]);Number.isNaN(o)||(i={type:"table",index:Number(o)})}return[t,i]}function io(r){const e=[];for(let t=0;t<r.length;t++){const n=r[t],i=Number.parseInt(n);if(!Number.isNaN(i))if(i===0)e.push({type:"resetAll"});else if(i<=9)Be[i]&&e.push({type:"setDecoration",value:Be[i]});else if(i<=29){const o=Be[i-20];o&&e.push({type:"resetDecoration",value:o})}else if(i<=37)e.push({type:"setForegroundColor",value:{type:"named",name:z[i-30]}});else if(i===38){const[o,a]=Tt(r,t);a&&e.push({type:"setForegroundColor",value:a}),t+=o}else if(i===39)e.push({type:"resetForegroundColor"});else if(i<=47)e.push({type:"setBackgroundColor",value:{type:"named",name:z[i-40]}});else if(i===48){const[o,a]=Tt(r,t);a&&e.push({type:"setBackgroundColor",value:a}),t+=o}else i===49?e.push({type:"resetBackgroundColor"}):i>=90&&i<=97?e.push({type:"setForegroundColor",value:{type:"named",name:z[i-90+8]}}):i>=100&&i<=107&&e.push({type:"setBackgroundColor",value:{type:"named",name:z[i-100+8]}})}return e}function oo(){let r=null,e=null,t=new Set;return{parse(n){const i=[];let o=0;do{const a=no(n,o),l=a.sequence?n.substring(o,a.startPosition):n.substring(o);if(l.length>0&&i.push({value:l,foreground:r,background:e,decorations:new Set(t)}),a.sequence){const s=io(a.sequence);for(const c of s)c.type==="resetAll"?(r=null,e=null,t.clear()):c.type==="resetForegroundColor"?r=null:c.type==="resetBackgroundColor"?e=null:c.type==="resetDecoration"&&t.delete(c.value);for(const c of s)c.type==="setForegroundColor"?r=c.value:c.type==="setBackgroundColor"?e=c.value:c.type==="setDecoration"&&t.add(c.value)}o=a.position}while(o<n.length);return i}}}var ao={black:"#000000",red:"#bb0000",green:"#00bb00",yellow:"#bbbb00",blue:"#0000bb",magenta:"#ff00ff",cyan:"#00bbbb",white:"#eeeeee",brightBlack:"#555555",brightRed:"#ff5555",brightGreen:"#00ff00",brightYellow:"#ffff55",brightBlue:"#5555ff",brightMagenta:"#ff55ff",brightCyan:"#55ffff",brightWhite:"#ffffff"};function so(r=ao){function e(l){return r[l]}function t(l){return`#${l.map(s=>Math.max(0,Math.min(s,255)).toString(16).padStart(2,"0")).join("")}`}let n;function i(){if(n)return n;n=[];for(let c=0;c<z.length;c++)n.push(e(z[c]));let l=[0,95,135,175,215,255];for(let c=0;c<6;c++)for(let m=0;m<6;m++)for(let d=0;d<6;d++)n.push(t([l[c],l[m],l[d]]));let s=8;for(let c=0;c<24;c++,s+=10)n.push(t([s,s,s]));return n}function o(l){return i()[l]}function a(l){switch(l.type){case"named":return e(l.name);case"rgb":return t(l.rgb);case"table":return o(l.index)}}return{value:a}}function lo(r,e,t){const n=Re(r,t),i=Se(e),o=so(Object.fromEntries(z.map(l=>[l,r.colors?.[`terminal.ansi${l[0].toUpperCase()}${l.substring(1)}`]]))),a=oo();return i.map(l=>a.parse(l[0]).map(s=>{let c,m;s.decorations.has("reverse")?(c=s.background?o.value(s.background):r.bg,m=s.foreground?o.value(s.foreground):r.fg):(c=s.foreground?o.value(s.foreground):r.fg,m=s.background?o.value(s.background):void 0),c=F(c,n),m=F(m,n),s.decorations.has("dim")&&(c=uo(c));let d=$.None;return s.decorations.has("bold")&&(d|=$.Bold),s.decorations.has("italic")&&(d|=$.Italic),s.decorations.has("underline")&&(d|=$.Underline),{content:s.value,offset:l[1],color:c,bgColor:m,fontStyle:d}}))}function uo(r){const e=r.match(/#([0-9a-f]{3})([0-9a-f]{3})?([0-9a-f]{2})?/);if(e)if(e[3]){const n=Math.round(Number.parseInt(e[3],16)/2).toString(16).padStart(2,"0");return`#${e[1]}${e[2]}${n}`}else return e[2]?`#${e[1]}${e[2]}80`:`#${Array.from(e[1]).map(n=>`${n}${n}`).join("")}80`;const t=r.match(/var\((--[\w-]+-ansi-[\w-]+)\)/);return t?`var(${t[1]}-dim)`:r}function rt(r,e,t={}){const{lang:n="text",theme:i=r.getLoadedThemes()[0]}=t;if(et(n)||tt(i))return Se(e).map(s=>[{content:s[0],offset:s[1]}]);const{theme:o,colorMap:a}=r.setTheme(i);if(n==="ansi")return lo(o,e,t);const l=r.getLanguage(n);if(t.grammarState){if(t.grammarState.lang!==l.name)throw new W(`Grammar state language "${t.grammarState.lang}" does not match highlight language "${l.name}"`);if(!t.grammarState.themes.includes(o.name))throw new W(`Grammar state themes "${t.grammarState.themes}" do not contain highlight theme "${o.name}"`)}return mo(e,l,o,a,t)}function co(...r){if(r.length===2)return ae(r[1]);const[e,t,n={}]=r,{lang:i="text",theme:o=e.getLoadedThemes()[0]}=n;if(et(i)||tt(o))throw new W("Plain language does not have grammar state");if(i==="ansi")throw new W("ANSI language does not have grammar state");const{theme:a,colorMap:l}=e.setTheme(o),s=e.getLanguage(i);return new Z(Ae(t,s,a,l,n).stateStack,s.name,a.name)}function mo(r,e,t,n,i){const o=Ae(r,e,t,n,i),a=new Z(Ae(r,e,t,n,i).stateStack,e.name,t.name);return Ie(o.tokens,a),o.tokens}function Ae(r,e,t,n,i){const o=Re(t,i),{tokenizeMaxLineLength:a=0,tokenizeTimeLimit:l=500}=i,s=Se(r);let c=i.grammarState?Zi(i.grammarState,t.name)??He:i.grammarContextCode!=null?Ae(i.grammarContextCode,e,t,n,{...i,grammarState:void 0,grammarContextCode:void 0}).stateStack:He,m=[];const d=[];for(let g=0,p=s.length;g<p;g++){const[f,b]=s[g];if(f===""){m=[],d.push([]);continue}if(a>0&&f.length>=a){m=[],d.push([{content:f,offset:b,color:"",fontStyle:0}]);continue}let y,v,E;i.includeExplanation&&(y=e.tokenizeLine(f,c),v=y.tokens,E=0);const R=e.tokenizeLine2(f,c,l),S=R.tokens.length/2;for(let T=0;T<S;T++){const M=R.tokens[2*T],H=T+1<S?R.tokens[2*T+2]:f.length;if(M===H)continue;const it=R.tokens[2*T+1],_r=F(n[Y.getForeground(it)],o),gr=Y.getFontStyle(it),Oe={content:f.substring(M,H),offset:b+M,color:_r,fontStyle:gr};if(i.includeExplanation){const ot=[];if(i.includeExplanation!=="scopeName")for(const j of t.settings){let K;switch(typeof j.scope){case"string":K=j.scope.split(/,/).map(Ce=>Ce.trim());break;case"object":K=j.scope;break;default:continue}ot.push({settings:j,selectors:K.map(Ce=>Ce.split(/ /))})}Oe.explanation=[];let at=0;for(;M+at<H;){const j=v[E],K=f.substring(j.startIndex,j.endIndex);at+=K.length,Oe.explanation.push({content:K,scopes:i.includeExplanation==="scopeName"?po(j.scopes):ho(ot,j.scopes)}),E+=1}}m.push(Oe)}d.push(m),m=[],c=R.ruleStack}return{tokens:d,stateStack:c}}function po(r){return r.map(e=>({scopeName:e}))}function ho(r,e){const t=[];for(let n=0,i=e.length;n<i;n++){const o=e[n];t[n]={scopeName:o,themeMatches:_o(r,o,e.slice(0,n))}}return t}function Pt(r,e){return r===e||e.substring(0,r.length)===r&&e[r.length]==="."}function fo(r,e,t){if(!Pt(r[r.length-1],e))return!1;let n=r.length-2,i=t.length-1;for(;n>=0&&i>=0;)Pt(r[n],t[i])&&(n-=1),i-=1;return n===-1}function _o(r,e,t){const n=[];for(const{selectors:i,settings:o}of r)for(const a of i)if(fo(a,e,t)){n.push(o);break}return n}function dr(r,e,t){const n=Object.entries(t.themes).filter(s=>s[1]).map(s=>({color:s[0],theme:s[1]})),i=n.map(s=>{const c=rt(r,e,{...t,theme:s.theme}),m=ae(c),d=typeof s.theme=="string"?s.theme:s.theme.name;return{tokens:c,state:m,theme:d}}),o=go(...i.map(s=>s.tokens)),a=o[0].map((s,c)=>s.map((m,d)=>{const g={content:m.content,variants:{},offset:m.offset};return"includeExplanation"in t&&t.includeExplanation&&(g.explanation=m.explanation),o.forEach((p,f)=>{const{content:b,explanation:y,offset:v,...E}=p[c][d];g.variants[n[f].color]=E}),g})),l=i[0].state?new Z(Object.fromEntries(i.map(s=>[s.theme,s.state?.getInternalStack(s.theme)])),i[0].state.lang):void 0;return l&&Ie(a,l),a}function go(...r){const e=r.map(()=>[]),t=r.length;for(let n=0;n<r[0].length;n++){const i=r.map(s=>s[n]),o=e.map(()=>[]);e.forEach((s,c)=>s.push(o[c]));const a=i.map(()=>0),l=i.map(s=>s[0]);for(;l.every(s=>s);){const s=Math.min(...l.map(c=>c.content.length));for(let c=0;c<t;c++){const m=l[c];m.content.length===s?(o[c].push(m),a[c]+=1,l[c]=i[c][a[c]]):(o[c].push({...m,content:m.content.slice(0,s)}),l[c]={...m,content:m.content.slice(s),offset:m.offset+s})}}}return e}function Le(r,e,t){let n,i,o,a,l,s;if("themes"in t){const{defaultColor:c="light",cssVariablePrefix:m="--shiki-"}=t,d=Object.entries(t.themes).filter(y=>y[1]).map(y=>({color:y[0],theme:y[1]})).sort((y,v)=>y.color===c?-1:v.color===c?1:0);if(d.length===0)throw new W("`themes` option must not be empty");const g=dr(r,e,t);if(s=ae(g),c&&!d.find(y=>y.color===c))throw new W(`\`themes\` option must contain the defaultColor key \`${c}\``);const p=d.map(y=>r.getTheme(y.theme)),f=d.map(y=>y.color);o=g.map(y=>y.map(v=>yo(v,f,m,c))),s&&Ie(o,s);const b=d.map(y=>Re(y.theme,t));i=d.map((y,v)=>(v===0&&c?"":`${m+y.color}:`)+(F(p[v].fg,b[v])||"inherit")).join(";"),n=d.map((y,v)=>(v===0&&c?"":`${m+y.color}-bg:`)+(F(p[v].bg,b[v])||"inherit")).join(";"),a=`shiki-themes ${p.map(y=>y.name).join(" ")}`,l=c?void 0:[i,n].join(";")}else if("theme"in t){const c=Re(t.theme,t);o=rt(r,e,t);const m=r.getTheme(t.theme);n=F(m.bg,c),i=F(m.fg,c),a=m.name,s=ae(o)}else throw new W("Invalid options, either `theme` or `themes` must be provided");return{tokens:o,fg:i,bg:n,themeName:a,rootStyle:l,grammarState:s}}function yo(r,e,t,n){const i={content:r.content,explanation:r.explanation,offset:r.offset},o=e.map(s=>cr(r.variants[s])),a=new Set(o.flatMap(s=>Object.keys(s))),l={};return o.forEach((s,c)=>{for(const m of a){const d=s[m]||"inherit";if(c===0&&n)l[m]=d;else{const g=m==="color"?"":m==="background-color"?"-bg":`-${m}`,p=t+e[c]+(m==="color"?"":g);l[p]=d}}}),i.htmlStyle=l,i}function Te(r,e,t,n={meta:{},options:t,codeToHast:(i,o)=>Te(r,i,o),codeToTokens:(i,o)=>Le(r,i,o)}){let i=e;for(const p of be(t))i=p.preprocess?.call(n,i,t)||i;let{tokens:o,fg:a,bg:l,themeName:s,rootStyle:c,grammarState:m}=Le(r,i,t);const{mergeWhitespaces:d=!0}=t;d===!0?o=vo(o):d==="never"&&(o=Ro(o));const g={...n,get source(){return i}};for(const p of be(t))o=p.tokens?.call(g,o)||o;return Eo(o,{...t,fg:a,bg:l,themeName:s,rootStyle:c},g,m)}function Eo(r,e,t,n=ae(r)){const i=be(e),o=[],a={type:"root",children:[]},{structure:l="classic",tabindex:s="0"}=e;let c={type:"element",tagName:"pre",properties:{class:`shiki ${e.themeName||""}`,style:e.rootStyle||`background-color:${e.bg};color:${e.fg}`,...s!==!1&&s!=null?{tabindex:s.toString()}:{},...Object.fromEntries(Array.from(Object.entries(e.meta||{})).filter(([f])=>!f.startsWith("_")))},children:[]},m={type:"element",tagName:"code",properties:{},children:o};const d=[],g={...t,structure:l,addClassToHast:lr,get source(){return t.source},get tokens(){return r},get options(){return e},get root(){return a},get pre(){return c},get code(){return m},get lines(){return d}};if(r.forEach((f,b)=>{b&&(l==="inline"?a.children.push({type:"element",tagName:"br",properties:{},children:[]}):l==="classic"&&o.push({type:"text",value:`
`}));let y={type:"element",tagName:"span",properties:{class:"line"},children:[]},v=0;for(const E of f){let R={type:"element",tagName:"span",properties:{...E.htmlAttrs},children:[{type:"text",value:E.content}]};E.htmlStyle;const S=Yi(E.htmlStyle||cr(E));S&&(R.properties.style=S);for(const T of i)R=T?.span?.call(g,R,b+1,v,y,E)||R;l==="inline"?a.children.push(R):l==="classic"&&y.children.push(R),v+=E.content.length}if(l==="classic"){for(const E of i)y=E?.line?.call(g,y,b+1)||y;d.push(y),o.push(y)}}),l==="classic"){for(const f of i)m=f?.code?.call(g,m)||m;c.children.push(m);for(const f of i)c=f?.pre?.call(g,c)||c;a.children.push(c)}let p=a;for(const f of i)p=f?.root?.call(g,p)||p;return n&&Ie(p,n),p}function vo(r){return r.map(e=>{const t=[];let n="",i=0;return e.forEach((o,a)=>{const s=!(o.fontStyle&&o.fontStyle&$.Underline);s&&o.content.match(/^\s+$/)&&e[a+1]?(i||(i=o.offset),n+=o.content):n?(s?t.push({...o,offset:i,content:n+o.content}):t.push({content:n,offset:i},o),i=0,n=""):t.push(o)}),t})}function Ro(r){return r.map(e=>e.flatMap(t=>{if(t.content.match(/^\s+$/))return t;const n=t.content.match(/^(\s*)(.*?)(\s*)$/);if(!n)return t;const[,i,o,a]=n;if(!i&&!a)return t;const l=[{...t,offset:t.offset+i.length,content:o}];return i&&l.unshift({content:i,offset:t.offset}),a&&l.push({content:a,offset:t.offset+i.length+o.length}),l}))}function bo(r,e,t){const n={meta:{},options:t,codeToHast:(o,a)=>Te(r,o,a),codeToTokens:(o,a)=>Le(r,o,a)};let i=Fi(Te(r,e,t,n));for(const o of be(t))i=o.postprocess?.call(n,i,t)||i;return i}const St={light:"#333333",dark:"#bbbbbb"},It={light:"#fffffe",dark:"#1e1e1e"},Ot="__shiki_resolved";function nt(r){if(r?.[Ot])return r;const e={...r};e.tokenColors&&!e.settings&&(e.settings=e.tokenColors,delete e.tokenColors),e.type||(e.type="dark"),e.colorReplacements={...e.colorReplacements},e.settings||(e.settings=[]);let{bg:t,fg:n}=e;if(!t||!n){const l=e.settings?e.settings.find(s=>!s.name&&!s.scope):void 0;l?.settings?.foreground&&(n=l.settings.foreground),l?.settings?.background&&(t=l.settings.background),!n&&e?.colors?.["editor.foreground"]&&(n=e.colors["editor.foreground"]),!t&&e?.colors?.["editor.background"]&&(t=e.colors["editor.background"]),n||(n=e.type==="light"?St.light:St.dark),t||(t=e.type==="light"?It.light:It.dark),e.fg=n,e.bg=t}e.settings[0]&&e.settings[0].settings&&!e.settings[0].scope||e.settings.unshift({settings:{foreground:e.fg,background:e.bg}});let i=0;const o=new Map;function a(l){if(o.has(l))return o.get(l);i+=1;const s=`#${i.toString(16).padStart(8,"0").toLowerCase()}`;return e.colorReplacements?.[`#${s}`]?a(l):(o.set(l,s),s)}e.settings=e.settings.map(l=>{const s=l.settings?.foreground&&!l.settings.foreground.startsWith("#"),c=l.settings?.background&&!l.settings.background.startsWith("#");if(!s&&!c)return l;const m={...l,settings:{...l.settings}};if(s){const d=a(l.settings.foreground);e.colorReplacements[d]=l.settings.foreground,m.settings.foreground=d}if(c){const d=a(l.settings.background);e.colorReplacements[d]=l.settings.background,m.settings.background=d}return m});for(const l of Object.keys(e.colors||{}))if((l==="editor.foreground"||l==="editor.background"||l.startsWith("terminal.ansi"))&&!e.colors[l]?.startsWith("#")){const s=a(e.colors[l]);e.colorReplacements[s]=e.colors[l],e.colors[l]=s}return Object.defineProperty(e,Ot,{enumerable:!1,writable:!1,value:!0}),e}async function hr(r){return Array.from(new Set((await Promise.all(r.filter(e=>!ar(e)).map(async e=>await ur(e).then(t=>Array.isArray(t)?t:[t])))).flat()))}async function fr(r){return(await Promise.all(r.map(async t=>sr(t)?null:nt(await ur(t))))).filter(t=>!!t)}class Ao extends wn{constructor(t,n,i,o={}){super(t);h(this,"_resolvedThemes",new Map);h(this,"_resolvedGrammars",new Map);h(this,"_langMap",new Map);h(this,"_langGraph",new Map);h(this,"_textmateThemeCache",new WeakMap);h(this,"_loadedThemesCache",null);h(this,"_loadedLanguagesCache",null);this._resolver=t,this._themes=n,this._langs=i,this._alias=o,this._themes.map(a=>this.loadTheme(a)),this.loadLanguages(this._langs)}getTheme(t){return typeof t=="string"?this._resolvedThemes.get(t):this.loadTheme(t)}loadTheme(t){const n=nt(t);return n.name&&(this._resolvedThemes.set(n.name,n),this._loadedThemesCache=null),n}getLoadedThemes(){return this._loadedThemesCache||(this._loadedThemesCache=[...this._resolvedThemes.keys()]),this._loadedThemesCache}setTheme(t){let n=this._textmateThemeCache.get(t);n||(n=_e.createFromRawTheme(t),this._textmateThemeCache.set(t,n)),this._syncRegistry.setTheme(n)}getGrammar(t){if(this._alias[t]){const n=new Set([t]);for(;this._alias[t];){if(t=this._alias[t],n.has(t))throw new k(`Circular alias \`${Array.from(n).join(" -> ")} -> ${t}\``);n.add(t)}}return this._resolvedGrammars.get(t)}loadLanguage(t){if(this.getGrammar(t.name))return;const n=new Set([...this._langMap.values()].filter(a=>a.embeddedLangsLazy?.includes(t.name)));this._resolver.addLanguage(t);const i={balancedBracketSelectors:t.balancedBracketSelectors||["*"],unbalancedBracketSelectors:t.unbalancedBracketSelectors||[]};this._syncRegistry._rawGrammars.set(t.scopeName,t);const o=this.loadGrammarWithConfiguration(t.scopeName,1,i);if(o.name=t.name,this._resolvedGrammars.set(t.name,o),t.aliases&&t.aliases.forEach(a=>{this._alias[a]=t.name}),this._loadedLanguagesCache=null,n.size)for(const a of n)this._resolvedGrammars.delete(a.name),this._loadedLanguagesCache=null,this._syncRegistry?._injectionGrammars?.delete(a.scopeName),this._syncRegistry?._grammars?.delete(a.scopeName),this.loadLanguage(this._langMap.get(a.name))}dispose(){super.dispose(),this._resolvedThemes.clear(),this._resolvedGrammars.clear(),this._langMap.clear(),this._langGraph.clear(),this._loadedThemesCache=null}loadLanguages(t){for(const o of t)this.resolveEmbeddedLanguages(o);const n=Array.from(this._langGraph.entries()),i=n.filter(([o,a])=>!a);if(i.length){const o=n.filter(([a,l])=>l&&l.embeddedLangs?.some(s=>i.map(([c])=>c).includes(s))).filter(a=>!i.includes(a));throw new k(`Missing languages ${i.map(([a])=>`\`${a}\``).join(", ")}, required by ${o.map(([a])=>`\`${a}\``).join(", ")}`)}for(const[o,a]of n)this._resolver.addLanguage(a);for(const[o,a]of n)this.loadLanguage(a)}getLoadedLanguages(){return this._loadedLanguagesCache||(this._loadedLanguagesCache=[...new Set([...this._resolvedGrammars.keys(),...Object.keys(this._alias)])]),this._loadedLanguagesCache}resolveEmbeddedLanguages(t){if(this._langMap.set(t.name,t),this._langGraph.set(t.name,t),t.embeddedLangs)for(const n of t.embeddedLangs)this._langGraph.set(n,this._langMap.get(n))}}class Lo{constructor(e,t){h(this,"_langs",new Map);h(this,"_scopeToLang",new Map);h(this,"_injections",new Map);h(this,"_onigLib");this._onigLib={createOnigScanner:n=>e.createScanner(n),createOnigString:n=>e.createString(n)},t.forEach(n=>this.addLanguage(n))}get onigLib(){return this._onigLib}getLangRegistration(e){return this._langs.get(e)}loadGrammar(e){return this._scopeToLang.get(e)}addLanguage(e){this._langs.set(e.name,e),e.aliases&&e.aliases.forEach(t=>{this._langs.set(t,e)}),this._scopeToLang.set(e.scopeName,e),e.injectTo&&e.injectTo.forEach(t=>{this._injections.get(t)||this._injections.set(t,[]),this._injections.get(t).push(e.scopeName)})}getInjections(e){const t=e.split(".");let n=[];for(let i=1;i<=t.length;i++){const o=t.slice(0,i).join(".");n=[...n,...this._injections.get(o)||[]]}return n}}let ee=0;function To(r){ee+=1,r.warnings!==!1&&ee>=10&&ee%10===0&&console.warn(`[Shiki] ${ee} instances have been created. Shiki is supposed to be used as a singleton, consider refactoring your code to cache your highlighter instance; Or call \`highlighter.dispose()\` to release unused instances.`);let e=!1;if(!r.engine)throw new k("`engine` option is required for synchronous mode");const t=(r.langs||[]).flat(1),n=(r.themes||[]).flat(1).map(nt),i=new Lo(r.engine,t),o=new Ao(i,n,t,r.langAlias);let a;function l(E){y();const R=o.getGrammar(typeof E=="string"?E:E.name);if(!R)throw new k(`Language \`${E}\` not found, you may need to load it first`);return R}function s(E){if(E==="none")return{bg:"",fg:"",name:"none",settings:[],type:"dark"};y();const R=o.getTheme(E);if(!R)throw new k(`Theme \`${E}\` not found, you may need to load it first`);return R}function c(E){y();const R=s(E);a!==E&&(o.setTheme(R),a=E);const S=o.getColorMap();return{theme:R,colorMap:S}}function m(){return y(),o.getLoadedThemes()}function d(){return y(),o.getLoadedLanguages()}function g(...E){y(),o.loadLanguages(E.flat(1))}async function p(...E){return g(await hr(E))}function f(...E){y();for(const R of E.flat(1))o.loadTheme(R)}async function b(...E){return y(),f(await fr(E))}function y(){if(e)throw new k("Shiki instance has been disposed")}function v(){e||(e=!0,o.dispose(),ee-=1)}return{setTheme:c,getTheme:s,getLanguage:l,getLoadedThemes:m,getLoadedLanguages:d,loadLanguage:p,loadLanguageSync:g,loadTheme:b,loadThemeSync:f,dispose:v,[Symbol.dispose]:v}}async function Po(r={}){r.loadWasm;const[e,t,n]=await Promise.all([fr(r.themes||[]),hr(r.langs||[]),r.engine||wt(r.loadWasm||Hr())]);return To({...r,themes:e,langs:t,engine:n})}async function So(r={}){const e=await Po(r);return{getLastGrammarState:(...t)=>co(e,...t),codeToTokensBase:(t,n)=>rt(e,t,n),codeToTokensWithThemes:(t,n)=>dr(e,t,n),codeToTokens:(t,n)=>Le(e,t,n),codeToHast:(t,n)=>Te(e,t,n),codeToHtml:(t,n)=>bo(e,t,n),...e,getInternalContext:()=>e}}function Io(r,e,t){let n,i,o;{const l=r;n=l.langs,i=l.themes,o=l.engine}async function a(l){function s(p){if(typeof p=="string"){if(ar(p))return[];const f=n[p];if(!f)throw new W(`Language \`${p}\` is not included in this bundle. You may want to load it from external source.`);return f}return p}function c(p){if(sr(p))return"none";if(typeof p=="string"){const f=i[p];if(!f)throw new W(`Theme \`${p}\` is not included in this bundle. You may want to load it from external source.`);return f}return p}const m=(l.themes??[]).map(p=>c(p)),d=(l.langs??[]).map(p=>s(p)),g=await So({engine:l.engine??o(),...l,themes:m,langs:d});return{...g,loadLanguage(...p){return g.loadLanguage(...p.map(s))},loadTheme(...p){return g.loadTheme(...p.map(c))}}}return a}const Do=Io({langs:Lr,themes:Pr,engine:()=>wt(u(()=>import("./wasm-CG6Dc4jp.js"),[],import.meta.url))});export{u as _,Do as c};
