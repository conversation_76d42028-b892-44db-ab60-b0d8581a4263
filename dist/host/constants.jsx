/**
 * Constants for SahAI CEP Extension - ExtendScript Side
 * This file contains shared constants between the CEP panel and ExtendScript
 */

// File paths - Using global variables instead of var for better scope
SETTINGS_FILE_PATH = "~/Adobe/CEP/extensions/SahAI/settings.json";
HISTORY_FILE_PATH = "~/Adobe/CEP/extensions/SahAI/history.json";
EXTENSION_DIR_PATH = "~/Adobe/CEP/extensions/SahAI";

// API defaults - Using global variables instead of var for better scope
DEFAULT_OLLAMA_URL = "http://localhost:11434";

// HTTP settings
DEFAULT_TIMEOUT = 10000;
DEFAULT_RETRIES = 2;

// Port numbers
OLLAMA_DEFAULT_PORT = 11434;
HTTPS_DEFAULT_PORT = 443;

// Context lengths (common defaults)
DEFAULT_CONTEXT_LENGTH = 4096;
SMALL_CONTEXT_LENGTH = 8192;
MEDIUM_CONTEXT_LENGTH = 32000;
<PERSON>R<PERSON>_CONTEXT_LENGTH = 128000;
EXTRA_LARGE_CONTEXT_LENGTH = 200000;

// Note: ExtendScript doesn't support CommonJS modules
// Constants are available globally when this file is included
